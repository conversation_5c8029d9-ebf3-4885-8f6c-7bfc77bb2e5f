import React, { useState, useEffect } from 'react';

interface ScrollToBottomButtonProps {
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  isPortalMode?: boolean;
}

const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({ 
  messagesContainerRef, 
  isPortalMode = false 
}) => {
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
      setShowButton(!isNearBottom);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [messagesContainerRef]);

  const scrollToBottom = () => {
    const container = messagesContainerRef.current;
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  if (!isPortalMode) return null;

  return (
    <button
      className={`scroll-to-bottom ${showButton ? 'show' : ''}`}
      onClick={scrollToBottom}
      aria-label="Scroll to bottom"
      title="Scroll to bottom"
    >
      ↓
    </button>
  );
};

export default ScrollToBottomButton;
