# QuoteAI Portal Server Configuration Guide

## Overview

This guide covers the server-side configuration needed to deploy the QuoteAI portal on your Hostinger WordPress hosting environment. The portal requires specific URL routing, file permissions, and server settings to function properly.

## Hostinger-Specific Configuration

### 1. File Upload and Permissions

#### Upload Portal Files
1. **Access File Manager**: Log into Hostinger control panel → File Manager
2. **Navigate to Theme Directory**: Go to `/public_html/wp-content/themes/your-active-theme/`
3. **Create Portal Directory**: Create folder `quoteai-portal/`
4. **Upload Assets**: Upload the following structure:
   ```
   quoteai-portal/
   ├── assets/
   │   ├── portal.js
   │   ├── portal.css
   │   └── favicon.ico (optional)
   └── README.md (optional)
   ```

#### Set File Permissions
- **Directories**: 755 (rwxr-xr-x)
- **Files**: 644 (rw-r--r--)
- **PHP Files**: 644 (rw-r--r--)

```bash
# If you have SSH access:
find /path/to/quoteai-portal -type d -exec chmod 755 {} \;
find /path/to/quoteai-portal -type f -exec chmod 644 {} \;
```

### 2. WordPress Configuration

#### Add Portal Functions
1. **Edit functions.php**: Add the portal functions to your theme's `functions.php` file
2. **Alternative**: Create a custom plugin with the portal functions
3. **Upload Template**: Upload `page-chat.php` to your theme's root directory

#### WordPress Settings
1. **Permalinks**: Go to Settings → Permalinks
2. **Structure**: Ensure permalinks are set to "Post name" or custom structure
3. **Save Changes**: Click "Save Changes" to flush rewrite rules

### 3. URL Routing Configuration

#### WordPress Rewrite Rules
The portal uses WordPress's built-in URL rewriting. The following rule is added via the functions:

```php
add_rewrite_rule(
    '^chat/([^/]+)/?$',
    'index.php?pagename=chat&tradie_name=$matches[1]',
    'top'
);
```

#### .htaccess Configuration
Ensure your `.htaccess` file includes WordPress's standard rewrite rules:

```apache
# BEGIN WordPress
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
# END WordPress
```

### 4. Server Requirements

#### PHP Requirements
- **PHP Version**: 7.4 or higher (8.0+ recommended)
- **Memory Limit**: 256MB minimum (512MB recommended)
- **Max Execution Time**: 30 seconds minimum
- **File Upload Size**: 10MB minimum (for image uploads)

#### Apache Modules (Usually enabled on Hostinger)
- `mod_rewrite` (for URL rewriting)
- `mod_headers` (for CORS headers if needed)
- `mod_ssl` (for HTTPS)

### 5. SSL/HTTPS Configuration

#### Enable SSL
1. **Hostinger SSL**: Enable free SSL certificate in Hostinger control panel
2. **Force HTTPS**: Add redirect rules to force HTTPS
3. **WordPress Settings**: Update WordPress URLs to use HTTPS

#### HTTPS Redirect (add to .htaccess)
```apache
# Force HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## API Configuration

### 1. CORS Settings

If your QuoteAI API is hosted on a different domain, configure CORS headers:

```apache
# Add to .htaccess if needed
Header always set Access-Control-Allow-Origin "https://your-api-domain.com"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
```

### 2. API Endpoint Configuration

Update the portal configuration with your production API settings:

```javascript
// In portal.js or WordPress settings
window.QUOTE_AI_CONFIG = {
    apiUrl: 'https://your-api-domain.com/api',
    apiKey: 'your-production-api-key',
    websiteUrl: 'quoteai.com.au'
};
```

## Performance Optimization

### 1. Caching Configuration

#### WordPress Caching
1. **Install Caching Plugin**: LiteSpeed Cache (recommended for Hostinger)
2. **Configure Cache**: Enable page caching, exclude portal pages if needed
3. **CDN Setup**: Configure Cloudflare or similar CDN

#### Cache Exclusions
Exclude portal pages from caching to ensure real-time functionality:

```php
// Add to functions.php
function exclude_portal_from_cache($excluded) {
    if (get_query_var('tradie_name')) {
        return true;
    }
    return $excluded;
}
add_filter('litespeed_cache_is_cacheable', 'exclude_portal_from_cache');
```

### 2. Asset Optimization

#### Minification
- **CSS**: Minify portal.css for production
- **JavaScript**: Minify portal.js for production
- **Images**: Optimize any portal images

#### Compression
Enable Gzip compression in .htaccess:

```apache
# Enable Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

## Security Configuration

### 1. File Security

#### Protect Portal Files
Add security headers to .htaccess:

```apache
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
```

#### Prevent Direct Access
```apache
# Prevent direct access to portal assets
<Files "portal.js">
    Order Allow,Deny
    Allow from all
</Files>
```

### 2. Input Validation

The portal includes server-side validation for tradie names:

```php
function quoteai_portal_validate_tradie_name($tradie_name) {
    // Only allow alphanumeric characters, hyphens, and underscores
    if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $tradie_name)) {
        return false;
    }
    
    // Limit length
    if (strlen($tradie_name) > 50) {
        return false;
    }
    
    return true;
}
```

## Monitoring and Logging

### 1. Error Logging

#### WordPress Debug Logging
Enable WordPress debug logging in wp-config.php:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

#### Portal-Specific Logging
Add custom logging for portal events:

```php
function quoteai_portal_log($message) {
    if (WP_DEBUG_LOG) {
        error_log('[QuoteAI Portal] ' . $message);
    }
}
```

### 2. Performance Monitoring

#### Server Monitoring
- Monitor server response times
- Track memory usage
- Monitor disk space

#### Portal Analytics
- Track portal page views
- Monitor chat interactions
- Track error rates

## Troubleshooting

### Common Issues

#### 1. 404 Errors on Portal URLs
**Solution**: Flush permalinks
1. Go to Settings → Permalinks
2. Click "Save Changes"
3. Test portal URLs

#### 2. Portal Not Loading
**Checks**:
- Verify all files are uploaded
- Check file permissions
- Verify WordPress functions are added
- Check browser console for errors

#### 3. JavaScript Errors
**Checks**:
- Verify React CDN links are accessible
- Check portal.js file integrity
- Verify API configuration
- Check browser compatibility

#### 4. Mobile Issues
**Checks**:
- Test on actual devices
- Verify viewport meta tag
- Check touch event handling
- Verify safe area CSS

### Debug Commands

#### Check Rewrite Rules
```php
// Add to functions.php temporarily
function debug_rewrite_rules() {
    global $wp_rewrite;
    echo '<pre>';
    print_r($wp_rewrite->rules);
    echo '</pre>';
}
add_action('wp_footer', 'debug_rewrite_rules');
```

#### Check Portal Configuration
```javascript
// Browser console
console.log('Portal Config:', window.QUOTE_AI_CONFIG);
console.log('Current URL:', window.location.href);
console.log('Tradie Name:', new URLSearchParams(window.location.search).get('tradie'));
```

## Backup and Recovery

### 1. Backup Strategy
- **Files**: Backup portal files regularly
- **Database**: Include WordPress database in backups
- **Configuration**: Document all custom settings

### 2. Recovery Procedures
- **File Restoration**: Restore portal files from backup
- **Database Recovery**: Restore WordPress database
- **Configuration Reset**: Recreate portal settings

## Support and Maintenance

### Regular Maintenance Tasks
1. **Update Dependencies**: Keep React CDN links updated
2. **Monitor Performance**: Check portal loading times
3. **Security Updates**: Keep WordPress and plugins updated
4. **Backup Verification**: Test backup restoration procedures

### Getting Help
- **WordPress Support**: Hostinger WordPress support
- **Portal Issues**: Check browser console and WordPress error logs
- **API Issues**: Contact QuoteAI API support
- **Performance Issues**: Monitor server resources and optimize as needed
