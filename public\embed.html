<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Widget Embed Example</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    pre {
      background-color: #f8f8f8;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      border: 1px solid #ddd;
    }
    code {
      font-family: monospace;
    }
    .note {
      background-color: #fffde7;
      padding: 15px;
      border-left: 4px solid #ffd600;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Widget Embed Instructions</h1>
    
    <p>To add the QuoteAI chat widget to your website, simply add the following script tag to your HTML, preferably just before the closing <code>&lt;/body&gt;</code> tag:</p>
    
    <pre><code>&lt;script src="https://quoteai-firebase.web.app/chatbot-widget.umd.js"&gt;&lt;/script&gt;</code></pre>
    
    <div class="note">
      <strong>Note:</strong> The widget will automatically initialize and appear on your website. No additional configuration is required.
    </div>
    
    <h2>Advanced Configuration (Optional)</h2>
    
    <p>If you need to customize the widget, you can use the following code:</p>
    
    <pre><code>&lt;script src="https://quoteai-firebase.web.app/chatbot-widget.umd.js"&gt;&lt;/script&gt;
&lt;script&gt;
  // Optional: Configure the widget with custom settings
  window.initChatWidget({
    apiUrl: "https://api.quoteai.com" // Optional: Override the API URL
  });
&lt;/script&gt;</code></pre>
    
    <h2>How It Works</h2>
    
    <p>The QuoteAI widget automatically detects your website's domain and uses it to authenticate with our servers. This means:</p>
    
    <ul>
      <li>No API keys or credentials need to be exposed in your website code</li>
      <li>The widget is automatically customized for your business</li>
      <li>User conversations are securely stored and associated with your account</li>
    </ul>
    
    <p>If you have any questions or need assistance, please contact our support team.</p>
  </div>
</body>
</html>
