<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Integration Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    .test-section {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .test-section h2 {
      margin-top: 0;
    }
    .test-button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    .test-button:hover {
      background-color: #45a049;
    }
    .log-container {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 4px;
      max-height: 200px;
      overflow-y: auto;
    }
    .log-entry {
      margin: 5px 0;
      font-family: monospace;
    }
    .success {
      color: green;
    }
    .error {
      color: red;
    }
    .info {
      color: blue;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Integration Test</h1>

    <div class="test-section">
      <h2>Test Configuration</h2>
      <div>
        <label for="api-url">API URL:</label>
        <input type="text" id="api-url" value="http://localhost:8080" style="width: 300px;">
      </div>
      <div style="margin-top: 10px;">
        <label for="api-key">API Key:</label>
        <input type="text" id="api-key" value=";C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL" style="width: 300px;">
      </div>
      <div style="margin-top: 10px;">
        <label for="client-id">Client ID:</label>
        <input type="text" id="client-id" value="test-client" style="width: 300px;">
      </div>
      <div style="margin-top: 10px;">
        <label for="customer-name">Customer Name:</label>
        <input type="text" id="customer-name" value="syed_plumbing_uuid" style="width: 300px;">
      </div>
    </div>

    <div class="test-section">
      <h2>Test 1: Initialize Conversation</h2>
      <p>This test will call the /initiate_conversation endpoint to create a new thread.</p>
      <button id="test-init" class="test-button">Run Test</button>
      <div id="init-log" class="log-container"></div>
    </div>

    <div class="test-section">
      <h2>Test 2: Send Message</h2>
      <div style="margin-bottom: 10px;">
        <label for="thread-id">Thread ID:</label>
        <input type="text" id="thread-id" style="width: 300px;">
      </div>
      <div style="margin-bottom: 10px;">
        <label for="message">Message:</label>
        <input type="text" id="message" value="Hello, I need a quote for plumbing work" style="width: 300px;">
      </div>
      <button id="test-message" class="test-button">Send Message</button>
      <div id="message-log" class="log-container"></div>
    </div>

    <div class="test-section">
      <h2>Test 3: Initialize Widget</h2>
      <p>This test will initialize the ChatWidget component.</p>
      <button id="test-widget" class="test-button">Initialize Widget</button>
      <div id="widget-log" class="log-container"></div>
    </div>
  </div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

  <!-- Load the ChatWidget component -->
  <script src="/dist/chatbot-widget.umd.js" type="text/javascript"></script>
  <link rel="stylesheet" href="/dist/style.css">

  <script>
    // Helper function to log messages
    function log(containerId, message, type = 'info') {
      const container = document.getElementById(containerId);
      const entry = document.createElement('div');
      entry.className = `log-entry ${type}`;
      entry.textContent = message;
      container.appendChild(entry);
      container.scrollTop = container.scrollHeight;
    }

    // Test 1: Initialize Conversation
    document.getElementById('test-init').addEventListener('click', async () => {
      const apiUrl = document.getElementById('api-url').value;
      const apiKey = document.getElementById('api-key').value;
      const clientId = document.getElementById('client-id').value;
      const customerName = document.getElementById('customer-name').value;

      log('init-log', `Initializing conversation with customer_name: ${customerName}`, 'info');

      try {
        const response = await fetch(`${apiUrl}/initiate_conversation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Client-ID': clientId,
            'x-api-key': apiKey,
          },
          body: JSON.stringify({ customer_name: customerName }),
        });

        const data = await response.json();

        if (response.ok) {
          log('init-log', `Success! Thread ID: ${data.thread_id}`, 'success');
          document.getElementById('thread-id').value = data.thread_id;
        } else {
          log('init-log', `Error: ${JSON.stringify(data)}`, 'error');
        }
      } catch (error) {
        log('init-log', `Exception: ${error.message}`, 'error');
      }
    });

    // Test 2: Send Message
    document.getElementById('test-message').addEventListener('click', async () => {
      const apiUrl = document.getElementById('api-url').value;
      const apiKey = document.getElementById('api-key').value;
      const clientId = document.getElementById('client-id').value;
      const threadId = document.getElementById('thread-id').value;
      const message = document.getElementById('message').value;

      if (!threadId) {
        log('message-log', 'Error: Thread ID is required', 'error');
        return;
      }

      log('message-log', `Sending message: "${message}" to thread: ${threadId}`, 'info');

      try {
        const response = await fetch(`${apiUrl}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Client-ID': clientId,
            'x-api-key': apiKey,
          },
          body: JSON.stringify({
            thread_id: threadId,
            message: message,
            attachments: []
          }),
        });

        const data = await response.json();

        if (response.ok) {
          log('message-log', `Response: ${data.response}`, 'success');
        } else {
          log('message-log', `Error: ${JSON.stringify(data)}`, 'error');
        }
      } catch (error) {
        log('message-log', `Exception: ${error.message}`, 'error');
      }
    });

    // Test 3: Initialize Widget
    document.getElementById('test-widget').addEventListener('click', () => {
      const apiUrl = document.getElementById('api-url').value;
      const apiKey = document.getElementById('api-key').value;
      const clientId = document.getElementById('client-id').value;
      const customerName = document.getElementById('customer-name').value;

      log('widget-log', 'Initializing ChatWidget component...', 'info');

      try {
        if (typeof window.initChatWidget === 'function') {
          window.initChatWidget({
            clientId: clientId,
            apiKey: apiKey,
            uuid: customerName,
            apiUrl: apiUrl
          });

          log('widget-log', 'ChatWidget initialized successfully!', 'success');
        } else {
          log('widget-log', 'Error: initChatWidget function not available', 'error');
        }
      } catch (error) {
        log('widget-log', `Exception: ${error.message}`, 'error');
      }
    });
  </script>
</body>
</html>
