# QuoteAI Widget Integration Guide

This guide explains how to integrate the QuoteAI chat widget into your website to provide instant quote services to your visitors.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Basic Integration](#basic-integration)
3. [Configuration Options](#configuration-options)
4. [Advanced Customization](#advanced-customization)
5. [Troubleshooting](#troubleshooting)
6. [API Reference](#api-reference)

## Prerequisites

Before integrating the QuoteAI widget, you'll need:

1. A client ID (provided by QuoteAI)
2. An API key (provided by QuoteAI)

## Basic Integration

### Step 1: Add Required Scripts

Add the following scripts to your HTML file, preferably just before the closing `</body>` tag:

```html
<!-- Load React and ReactDOM from CDN -->
<script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
<script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

<!-- Load QuoteAI widget -->
<script type="module" src="https://quoteai-firebase.web.app/chatbot-widget.umd.js"></script>
```

For production environments, use the minified versions:

```html
<script src="https://unpkg.com/react@18.0.0/umd/react.production.min.js"></script>
<script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.production.min.js"></script>
```

### Step 2: Test the Integration

Once integrated, you should see a chat button in the bottom-right corner of your website. Click it to open the chat widget and test the functionality.

## Configuration Options

The `initChatWidget` function accepts a configuration object with the following properties:

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `clientId` | string | Yes | Your QuoteAI client identifier |
| `apiKey` | string | Yes | Your QuoteAI API key for authentication |
| `uuid` | string | Yes | Unique identifier for the current user |

## Advanced Customization

### Configuring the API Endpoint

The widget is configured to use `https://api.quoteai.com` as the default API endpoint. If you need to use a different endpoint:

1. For self-hosted deployments, modify the `.env.production` file before building:
   ```
   VITE_API_BASE_URL=https://your-api-endpoint.com
   ```

2. For runtime configuration, you can pass the API URL when initializing the widget:
   ```javascript
   window.initChatWidget({
     clientId: "YOUR_CLIENT_ID",
     apiKey: "YOUR_API_KEY",
     uuid: getOrCreateUserUUID(),
     apiUrl: "https://your-api-endpoint.com" // Optional custom API URL
   });
   ```
   Note: This requires implementing the `apiUrl` parameter in the widget code.

### Persisting User Conversations

To enable users to continue previous conversations, store the UUID in a cookie or local storage:

```javascript
function getOrCreateUserUUID() {
  let uuid = localStorage.getItem('quoteai_uuid');
  if (!uuid) {
    uuid = generateUUID();
    localStorage.setItem('quoteai_uuid', uuid);
  }
  return uuid;
}
```

### Programmatically Opening the Chat

You can programmatically open the chat by dispatching a custom event:

```javascript
function openQuoteAIChat() {
  const event = new CustomEvent('quoteai:open');
  document.dispatchEvent(event);
}

// Example: Open chat when a button is clicked
document.getElementById('get-quote-button').addEventListener('click', openQuoteAIChat);
```

Note: This feature requires implementation in the widget. Contact QuoteAI support to enable this functionality.

## Troubleshooting

### Widget Not Appearing

If the widget doesn't appear on your website:

1. Check the browser console for errors
2. Verify that all scripts are loading correctly
3. Ensure your client ID and API key are correct
4. Check if there are any Content Security Policy (CSP) restrictions on your website

### API Connection Issues

If the widget appears but doesn't connect to the API:

1. Verify your API key is valid
2. Check if your website has CORS restrictions
3. Ensure your server is accessible from the client's location

### Common Error Messages

| Error | Possible Cause | Solution |
|-------|----------------|----------|
| "initChatWidget is not available" | Script not loaded properly | Check script loading order and network requests |
| "Failed to initialize conversation" | API key or client ID issue | Verify credentials and API endpoint availability |
| "Failed to send message" | Network or server issue | Check network connectivity and server status |

## API Reference

### Backend Endpoints

The widget communicates with the following endpoints:

#### Initialize Conversation

```
POST http://localhost:8080/initiate_conversation
```

Headers:
- `Content-Type: application/json`
- `Authorization: Bearer YOUR_API_KEY`
- `Client-ID: YOUR_CLIENT_ID`
- `x-api-key: YOUR_API_KEY`

Body:
```json
{
  "uuid": "USER_UUID"
}
```

Response:
```json
{
  "thread_id": "CONVERSATION_THREAD_ID"
}
```

#### Send Message

```
POST http://localhost:8080/chat
```

Headers:
- `Content-Type: application/json`
- `Authorization: Bearer YOUR_API_KEY`
- `Client-ID: YOUR_CLIENT_ID`
- `x-api-key: YOUR_API_KEY`

Body:
```json
{
  "message": "USER_MESSAGE",
  "thread_id": "CONVERSATION_THREAD_ID"
}
```

Response:
```json
{
  "response": "BOT_RESPONSE"
}
```