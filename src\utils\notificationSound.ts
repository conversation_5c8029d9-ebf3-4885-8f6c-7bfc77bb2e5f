/**
 * Notification sound utility
 * Provides functions to play notification sounds
 */

// Base64-encoded notification sound (a simple "ding" sound)
const notificationSoundBase64 = "data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFTb25vdGhlcXVlLm9yZwBURU5DAAAAHQAAA1N3aXRjaCBQbHVzIMKpIE5DSCBTb2Z0d2FyZQBUSVQyAAAABgAAAzIyMzUAVFNTRQAAAA8AAANMYXZmNTcuODMuMTAwAAAAAAAAAAAAAAD/80DEAAAAA0gAAAAATEFNRTMuMTAwVVVVVVVVVVVVVUxBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQsRbAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQMSkAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV";

/**
 * Play a notification sound
 * This function creates an Audio element and plays the notification sound
 * It handles errors gracefully and logs them to the console
 */
export function playNotificationSound(): void {
  try {
    // Check if the browser supports the Audio API
    if (typeof Audio !== 'undefined') {
      const audio = new Audio(notificationSoundBase64);
      audio.volume = 0.5; // Set volume to 50%
      
      // Play the sound
      const playPromise = audio.play();
      
      // Handle promise rejection (happens in some browsers)
      if (playPromise !== undefined) {
        playPromise.catch((error) => {
          console.warn("Notification sound couldn't be played:", error);
        });
      }
    }
  } catch (error) {
    console.error("Error playing notification sound:", error);
  }
}

/**
 * Check if the browser supports audio
 * This can be used to determine if notification sounds can be played
 */
export function supportsAudio(): boolean {
  return typeof Audio !== 'undefined';
}
