<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuoteAI Chat Persistence Test (Simple)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1b8ae4;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background-color: #1b8ae4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #53a2be;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .instructions {
            background-color: #fffde7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #ffd600;
        }
    </style>
</head>
<body>
    <h1>QuoteAI Chat Persistence Test</h1>
    
    <div class="instructions">
        <h3>Instructions</h3>
        <p>This page helps you test the chat persistence functionality. Follow these steps:</p>
        <ol>
            <li>Open the chat widget by clicking the "Get a Free Quote!" button</li>
            <li>Send a few messages and upload an image</li>
            <li>Click the "Simulate Page Refresh" button below</li>
            <li>Verify that your chat history and images are still visible</li>
            <li>You can also close this tab and reopen it to test real persistence</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>Chat Persistence Test</h2>
        <button id="refreshButton">Simulate Page Refresh</button>
        <button id="clearStorageButton">Clear All Storage</button>
        <div class="result" id="storageInfo"></div>
    </div>

    <!-- Load React and ReactDOM from CDN -->
    <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

    <!-- Load chatbot widget -->
    <script type="module" src="/src/main.tsx"></script>

    <script>
        // Function to display localStorage info
        function displayStorageInfo() {
            const storageInfo = document.getElementById('storageInfo');
            const items = { ...localStorage };
            
            let html = '<h3>Current localStorage Contents:</h3>';
            
            if (Object.keys(items).length === 0) {
                html += '<p>No items in localStorage</p>';
            } else {
                html += '<ul>';
                for (const key in items) {
                    if (key.startsWith('quoteai_')) {
                        try {
                            const value = JSON.parse(items[key]);
                            const messageCount = value.messages ? value.messages.length : 0;
                            const expiryDate = value.expiresAt ? new Date(value.expiresAt).toLocaleString() : 'N/A';
                            
                            html += `<li><strong>${key}</strong>: 
                                    <br>- Messages: ${messageCount}
                                    <br>- Thread ID: ${value.threadId || 'None'}
                                    <br>- Expires: ${expiryDate}
                                    </li>`;
                        } catch (e) {
                            html += `<li><strong>${key}</strong>: ${items[key]}</li>`;
                        }
                    } else {
                        html += `<li><strong>${key}</strong>: ${items[key]}</li>`;
                    }
                }
                html += '</ul>';
            }
            
            storageInfo.innerHTML = html;
        }
        
        // Set up event listeners when DOM is ready
        document.addEventListener('DOMContentLoaded', (event) => {
            // Initialize widget
            if (typeof window.initChatWidget === 'function') {
                const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 15);
                window.initChatWidget({
                    clientId: "test-client-local",
                    apiKey: "test-key-local",
                    uuid: clientUUID,
                    apiUrl: "http://localhost:8080",
                });
            } else {
                console.error('initChatWidget is not available');
                // Try again after a short delay
                setTimeout(() => {
                    if (typeof window.initChatWidget === 'function') {
                        const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 15);
                        window.initChatWidget({
                            clientId: "test-client-local",
                            apiKey: "test-key-local",
                            uuid: clientUUID,
                            apiUrl: "http://localhost:8080",
                        });
                        console.log('Widget initialized after delay');
                    } else {
                        console.error('initChatWidget still not available after delay');
                    }
                }, 1000);
            }
            
            // Display localStorage info
            displayStorageInfo();
            
            // Simulate page refresh
            document.getElementById('refreshButton').addEventListener('click', function() {
                window.location.reload();
            });
            
            // Clear all storage
            document.getElementById('clearStorageButton').addEventListener('click', function() {
                localStorage.clear();
                displayStorageInfo();
                alert('All localStorage data cleared. Refresh the page to start fresh.');
            });
        });
    </script>
</body>
</html>
