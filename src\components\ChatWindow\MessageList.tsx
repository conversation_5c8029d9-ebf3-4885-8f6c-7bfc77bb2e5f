import React, { useEffect, useRef } from 'react';
import type { Message, Suggestion } from '../../types';
import MessageItem from './MessageItem';
import ScrollToBottomButton from './ScrollToBottomButton';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean; // For bot typing indicator
  queueLength: number;
  onSuggestionClick: (suggestion: Suggestion) => void;
  onImageClick: (url: string) => void;
  isPortalMode?: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  queueLength,
  onSuggestionClick,
  onImageClick,
  isPortalMode = false,
}) => {
  const messagesContainerRef = useRef<HTMLDivElement | null>(null);
  const chatEndRef = useRef<HTMLDivElement | null>(null);

  // Auto-scroll to bottom when new messages arrive or loading state changes
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    // Check if user was near the bottom before new content
    const { scrollTop, scrollHeight, clientHeight } = container;
    const wasNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    // Only auto-scroll if user was near the bottom (to avoid interrupting manual scrolling)
    if (wasNearBottom || messages.length === 1) { // Always scroll for first message
      // Use requestAnimationFrame for smooth scrolling
      requestAnimationFrame(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        });
      });
    }
  }, [messages, isLoading]); // Scroll on new messages or when loading indicator appears/disappears

  return (
    <>
      <div
        className={`chat-messages ${isPortalMode ? 'portal-mode' : ''}`}
        aria-live="polite"
        aria-atomic="false"
        ref={messagesContainerRef}
      >
        {messages.map((msg, index) => (
          <MessageItem
            key={`${msg.type}-${msg.timestamp}-${index}`} // More robust key
            message={msg}
            onSuggestionClick={onSuggestionClick}
            onImageClick={onImageClick}
          />
        ))}
        {isLoading && (
          <div className="loading-dots" aria-label="Bot is typing">
            <span>.</span>
            <span>.</span>
            <span>.</span>
          </div>
        )}
        {/* Queue status indicator - consider if this is part of messages or separate UI */}
        {queueLength > 0 && !isLoading && ( // Show only if not already showing main loading
            <div className="queue-indicator">
                {queueLength} message{queueLength > 1 ? 's' : ''} in queue
            </div>
        )}
        {/* Invisible element at the end for scroll reference */}
        <div ref={chatEndRef} style={{ height: '1px', visibility: 'hidden' }} aria-hidden="true" />
      </div>
      <ScrollToBottomButton messagesContainerRef={messagesContainerRef} isPortalMode={isPortalMode} />
    </>
  );
};

export default MessageList;