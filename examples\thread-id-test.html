<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Thread ID Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #1b8ae4;
    }
    .test-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background: #0c6cb3;
    }
    .log {
      background: #000;
      color: #0f0;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      height: 300px;
      overflow-y: auto;
      margin-top: 10px;
    }
    .storage-panel {
      margin-top: 20px;
      background: #e3f2fd;
      padding: 15px;
      border-radius: 5px;
    }
    .storage-item {
      margin-bottom: 10px;
      padding: 10px;
      background: #fff;
      border-radius: 4px;
      border-left: 4px solid #1b8ae4;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Thread ID Test</h1>

    <div class="test-panel">
      <h2>Test Controls</h2>
      <button id="initButton">Initialize Widget</button>
      <button id="checkStorage">Check Storage</button>
      <button id="clearStorage">Clear Storage</button>
      <button id="clearLog">Clear Log</button>
      <button id="checkThreadId">Check Thread ID</button>

      <div style="margin-top: 15px; padding: 10px; background: #fff; border-radius: 5px;">
        <h3>Send Test Message</h3>
        <input type="text" id="testMessage" placeholder="Enter test message" style="width: 300px; padding: 8px; margin-right: 10px;">
        <button id="sendTestMessage">Send Message</button>
        <button id="sendDirectMessage" style="margin-left: 10px; background: #ff9800;">Send Direct Message</button>
      </div>

      <div class="log" id="logOutput"></div>

      <div class="storage-panel" id="storagePanel">
        <h3>LocalStorage Contents</h3>
        <div id="storageContents"></div>
      </div>
    </div>
  </div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

  <!-- Load the built widget -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">

  <script>
    // Simple logging function
    function log(message) {
      const logOutput = document.getElementById('logOutput');
      const timestamp = new Date().toLocaleTimeString();
      logOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
      logOutput.scrollTop = logOutput.scrollHeight;
      console.log(`[${timestamp}] ${message}`);
    }

    // Function to display localStorage contents
    function displayStorage() {
      const storageContents = document.getElementById('storageContents');
      storageContents.innerHTML = '';

      let hasQuoteAIItems = false;

      // Loop through all localStorage items
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);

        // Only show QuoteAI related items
        if (key.startsWith('quoteai_')) {
          hasQuoteAIItems = true;

          try {
            const value = JSON.parse(localStorage.getItem(key));
            const item = document.createElement('div');
            item.className = 'storage-item';

            // Format the item nicely
            let html = `<strong>${key}</strong><br>`;

            if (value.threadId) {
              html += `<span style="color: green">Thread ID: ${value.threadId}</span><br>`;
            } else {
              html += `<span style="color: red">No Thread ID</span><br>`;
            }

            if (value.messages) {
              html += `Messages: ${value.messages.length}<br>`;
            }

            if (value.expiresAt) {
              const expiryDate = new Date(value.expiresAt);
              html += `Expires: ${expiryDate.toLocaleString()}<br>`;
            }

            item.innerHTML = html;
            storageContents.appendChild(item);
          } catch (e) {
            const item = document.createElement('div');
            item.className = 'storage-item';
            item.innerHTML = `<strong>${key}</strong><br>Error parsing: ${e.message}`;
            storageContents.appendChild(item);
          }
        }
      }

      // Show message if no items found
      if (!hasQuoteAIItems) {
        storageContents.innerHTML = '<p>No QuoteAI items in localStorage</p>';
      }
    }

    // Initialize when the page loads
    document.addEventListener('DOMContentLoaded', () => {
      log('Page loaded');

      // Check if initChatWidget is available
      if (typeof window.initChatWidget === 'function') {
        log('✅ initChatWidget is available');
      } else {
        log('❌ initChatWidget is NOT available');
      }

      // Display initial storage contents
      displayStorage();

      // Set up button event listeners
      document.getElementById('initButton').addEventListener('click', () => {
        try {
          log('Initializing widget...');

          if (typeof window.initChatWidget === 'function') {
            const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 9);
            log(`Using UUID: ${clientUUID}`);

            window.initChatWidget({
              clientId: "test-client-thread",
              apiKey: "test-key-thread",
              uuid: clientUUID,
              apiUrl: "http://localhost:8080",
            });
            log(`Widget initialized with UUID: ${clientUUID}`);

            // Check storage after initialization
            setTimeout(() => {
              log('Checking storage after initialization...');
              displayStorage();
            }, 2000);
          } else {
            log('ERROR: initChatWidget is not a function');
          }
        } catch (error) {
          log(`ERROR: ${error.message}`);
        }
      });

      document.getElementById('checkStorage').addEventListener('click', () => {
        log('Checking localStorage...');
        displayStorage();
      });

      document.getElementById('clearStorage').addEventListener('click', () => {
        log('Clearing localStorage...');

        // Only clear QuoteAI items
        for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i);
          if (key.startsWith('quoteai_')) {
            localStorage.removeItem(key);
          }
        }

        // Also clear current session
        localStorage.removeItem('quoteai_current_session');

        displayStorage();
        log('QuoteAI localStorage items cleared');
      });

      document.getElementById('clearLog').addEventListener('click', () => {
        document.getElementById('logOutput').innerHTML = '';
        log('Log cleared');
      });

      // Set up check thread ID button
      document.getElementById('checkThreadId').addEventListener('click', () => {
        log('Checking thread ID directly...');

        try {
          // Add a global function to expose the thread ID
          window.checkQuoteAIThreadId = function() {
            // Find all quoteai localStorage items
            const items = {};
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && key.startsWith('quoteai_')) {
                try {
                  items[key] = JSON.parse(localStorage.getItem(key));
                } catch (e) {
                  items[key] = localStorage.getItem(key);
                }
              }
            }
            return items;
          };

          // Call the function and log the result
          const storageItems = window.checkQuoteAIThreadId();
          log('Storage items found:');
          console.log('QuoteAI storage items:', storageItems);

          // Find thread ID in storage items
          let threadId = null;
          for (const key in storageItems) {
            if (storageItems[key] && storageItems[key].threadId) {
              threadId = storageItems[key].threadId;
              log(`Found thread ID in ${key}: ${threadId}`);
            }
          }

          if (!threadId) {
            log('No thread ID found in storage');
          }

          // Access the threadIdRef directly from the React component
          const chatWidget = document.querySelector('.chat-container');
          if (!chatWidget) {
            log('ERROR: Chat widget not found. Make sure it\'s initialized first.');
            return;
          }

          // Use console.dir to inspect the React component
          log('Inspecting chat widget in console. Check browser console for details.');
          console.dir(chatWidget);

          // Try to access React fiber
          const key = Object.keys(chatWidget).find(key => key.startsWith('__reactFiber$'));
          if (key) {
            const fiber = chatWidget[key];
            log('React fiber found. Checking component state...');
            console.dir(fiber);

            // Try to find the component instance
            let node = fiber;
            while (node) {
              if (node.memoizedState && node.memoizedProps) {
                log('Found component with state. Check console for details.');
                console.dir(node.memoizedState);
                break;
              }
              node = node.return;
            }
          } else {
            log('Could not access React internals');
          }
        } catch (error) {
          log(`Error checking thread ID: ${error.message}`);
        }
      });

      // Set up test message button
      document.getElementById('sendTestMessage').addEventListener('click', () => {
        const testMessage = document.getElementById('testMessage').value.trim();
        if (!testMessage) {
          log('Please enter a test message');
          return;
        }

        log(`Sending test message: "${testMessage}"`);

        // Find the chat input and send button
        try {
          // First, make sure the chat is open
          const chatButton = document.querySelector('.chat-button');
          if (chatButton) {
            // Check if chat is already open
            const chatContainer = document.querySelector('.chat-container.open');
            if (!chatContainer) {
              log('Opening chat window first...');
              chatButton.click();
              // Wait a moment for the chat to open
              setTimeout(() => {
                sendMessageToChat(testMessage);
              }, 500);
              return;
            }
          }

          // If chat is already open, send the message immediately
          sendMessageToChat(testMessage);
        } catch (error) {
          log(`ERROR sending test message: ${error.message}`);
        }
      });

      // Set up direct message button
      document.getElementById('sendDirectMessage').addEventListener('click', () => {
        const testMessage = document.getElementById('testMessage').value.trim();
        if (!testMessage) {
          log('Please enter a test message');
          return;
        }

        log(`Sending direct message: "${testMessage}"`);

        try {
          // First, make sure the chat is open
          const chatButton = document.querySelector('.chat-button');
          if (chatButton) {
            // Check if chat is already open
            const chatContainer = document.querySelector('.chat-container.open');
            if (!chatContainer) {
              log('Opening chat window first...');
              chatButton.click();
              // Wait a moment for the chat to open
              setTimeout(() => {
                sendDirectMessage(testMessage);
              }, 500);
              return;
            }
          }

          // If chat is already open, send the message immediately
          sendDirectMessage(testMessage);
        } catch (error) {
          log(`ERROR sending direct message: ${error.message}`);
        }
      });

      // Function to send a message to the chat
      function sendMessageToChat(message) {
        try {
          // Find the chat input
          const chatInput = document.querySelector('.chat-input input');
          if (!chatInput) {
            log('ERROR: Chat input not found. Make sure the chat widget is initialized and open.');
            return;
          }

          log('Found chat input, setting value...');

          // Set the input value
          chatInput.value = message;

          // Dispatch input event to update React state
          const inputEvent = new Event('input', { bubbles: true });
          chatInput.dispatchEvent(inputEvent);

          log('Input value set and event dispatched');

          // Wait a moment for React to update the state
          setTimeout(() => {
            // Verify the input value was set correctly
            log(`Input value before sending: "${chatInput.value}"`);

            // Find the send button
            const sendButton = document.querySelector('.send-button');
            if (!sendButton) {
              log('ERROR: Send button not found');
              return;
            }

            // Make sure the send button is enabled
            if (sendButton.disabled) {
              log('ERROR: Send button is disabled. Input value may not be properly set.');
              return;
            }

            log('Found send button, clicking...');

            // Click the send button
            sendButton.click();
            log('Test message sent successfully');

            // Clear the test message input
            document.getElementById('testMessage').value = '';

            // Log the current state of the input
            setTimeout(() => {
              log(`Chat input value after sending: "${chatInput.value}"`);
            }, 100);
          }, 200); // Wait 200ms for React to update
        } catch (error) {
          log(`ERROR in sendMessageToChat: ${error.message}`);
        }
      }

      // Function to send a message directly to the server
      function sendDirectMessage(message) {
        try {
          log('Preparing to send direct message to server...');

          // Get thread ID from storage
          const storageItems = window.checkQuoteAIThreadId ? window.checkQuoteAIThreadId() : {};
          let threadId = null;

          // Find thread ID in storage items
          for (const key in storageItems) {
            if (storageItems[key] && storageItems[key].threadId) {
              threadId = storageItems[key].threadId;
              log(`Found thread ID in ${key}: ${threadId}`);
              break;
            }
          }

          if (!threadId) {
            log('ERROR: No thread ID found in storage. Cannot send message.');
            return;
          }

          // Prepare API request
          const API_BASE_URL = 'http://localhost:8080';
          log(`Sending direct request to ${API_BASE_URL}/chat`);

          fetch(`${API_BASE_URL}/chat`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Client-ID': 'test-client-thread',
              'x-api-key': 'test-key-thread',
            },
            body: JSON.stringify({
              message: message,
              thread_id: threadId,
              attachments: []
            }),
          })
          .then(response => {
            log(`Server response status: ${response.status}`);
            if (!response.ok) {
              throw new Error(`Server returned ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            log(`Server response: ${JSON.stringify(data)}`);
            log('Message sent successfully!');

            // Clear the test message input
            document.getElementById('testMessage').value = '';
          })
          .catch(error => {
            log(`ERROR: ${error.message}`);
          });
        } catch (error) {
          log(`ERROR in sendDirectMessage: ${error.message}`);
        }
      }
    });
  </script>
</body>
</html>
