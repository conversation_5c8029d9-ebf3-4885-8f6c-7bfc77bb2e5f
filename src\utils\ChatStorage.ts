import { Message } from '../types';
import { SESSION_EXPIRY_MS, CHAT_STORAGE_PREFIX, CURRENT_SESSION_ID_KEY } from '../constants';

interface ChatSession {
  id: string;
  messages: Message[];
  createdAt: number;
  expiresAt: number;
  threadId: string | null;
}

class ChatStorage {
  private readonly storagePrefix = CHAT_STORAGE_PREFIX;
  private currentSessionIdInternal: string | null = null;

  constructor() {
    this.currentSessionIdInternal = this.loadCurrentSessionIdFromStorage();
    this.cleanupExpiredSessions();
  }

  private loadCurrentSessionIdFromStorage(): string | null {
    try {
      const storedId = localStorage.getItem(CURRENT_SESSION_ID_KEY);
      if (!storedId) return null;

      // Basic validation if it looks like a session ID we might generate
      if (storedId.startsWith('session_')) {
         return storedId;
      }
      // For backward compatibility, try to parse if it looks like JSON string
      if (storedId.startsWith('"') && storedId.endsWith('"')) {
        try {
          const parsedId = JSON.parse(storedId);
          if (typeof parsedId === 'string' && parsedId.startsWith('session_')) {
            return parsedId;
          }
        } catch { /* Ignore parsing error, proceed as if not found or invalid */ }
      }
      // If it doesn't look like our session ID format, or parsing failed, clear it.
      localStorage.removeItem(CURRENT_SESSION_ID_KEY);
      return null;

    } catch (error) {
      console.error('ChatStorage: Error loading current session ID:', error);
      return null;
    }
  }

  private saveCurrentSessionIdToStorage(sessionId: string | null): void {
    if (sessionId) {
      localStorage.setItem(CURRENT_SESSION_ID_KEY, sessionId);
    } else {
      localStorage.removeItem(CURRENT_SESSION_ID_KEY);
    }
  }
  
  public getCurrentSessionId(): string | null {
    return this.currentSessionIdInternal;
  }

  public initSession(): string {
    if (this.currentSessionIdInternal) {
      const session = this.getSession(this.currentSessionIdInternal);
      if (session && session.expiresAt > Date.now()) {
        this.updateSessionExpiry(this.currentSessionIdInternal);
        return this.currentSessionIdInternal;
      }
    }

    const sessionId = this.generateSessionId();
    const now = Date.now();
    const newSession: ChatSession = {
      id: sessionId,
      messages: [],
      createdAt: now,
      expiresAt: now + SESSION_EXPIRY_MS,
      threadId: null,
    };

    this.saveSession(newSession);
    this.currentSessionIdInternal = sessionId;
    this.saveCurrentSessionIdToStorage(sessionId);
    return sessionId;
  }

  public saveThreadId(sessionId: string, threadId: string): void {
    const session = this.getSession(sessionId);
    if (session) {
      session.threadId = threadId;
      this.saveSession(session);
    } else {
      console.warn(`ChatStorage: Session ${sessionId} not found to save thread ID.`);
    }
  }

  public getThreadId(sessionId: string): string | null {
    if (!sessionId) return null;
    const session = this.getSession(sessionId);
    return session?.threadId || null;
  }

  public saveMessages(sessionId: string, messages: Message[]): void {
    if (!sessionId) return;
    const session = this.getSession(sessionId);
    if (session) {
      session.messages = messages;
      this.updateSessionExpiry(sessionId); // Also update expiry on message save
      this.saveSession(session);
    }
  }

  public getMessages(sessionId: string): Message[] {
    if (!sessionId) return [];
    const session = this.getSession(sessionId);
    return session?.messages || [];
  }

  public clearSession(sessionId: string): void {
    if (sessionId) {
      localStorage.removeItem(this.getSessionKey(sessionId));
      if (this.currentSessionIdInternal === sessionId) {
        this.currentSessionIdInternal = null;
        this.saveCurrentSessionIdToStorage(null);
      }
    }
  }
  
  public clearCurrentSession(): void {
    if (this.currentSessionIdInternal) {
        this.clearSession(this.currentSessionIdInternal);
    }
  }


  public isSessionExpired(sessionId: string): boolean {
    if (!sessionId) return true;
    const session = this.getSession(sessionId);
    return !session || session.expiresAt < Date.now();
  }

  private cleanupExpiredSessions(): void {
    const now = Date.now();
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(this.storagePrefix)) {
        try {
          const sessionData = localStorage.getItem(key);
          if (sessionData) {
            const session = JSON.parse(sessionData) as ChatSession;
            if (session.expiresAt < now) {
              localStorage.removeItem(key);
              // If the expired session was the current one, clear it
              if (this.currentSessionIdInternal === session.id) {
                this.currentSessionIdInternal = null;
                this.saveCurrentSessionIdToStorage(null);
              }
            }
          }
        } catch (error) {
          localStorage.removeItem(key); // Corrupted data
        }
      }
    });
  }

  private generateSessionId(): string {
    return `session_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}_${Date.now().toString(36)}`;
  }

  private getSessionKey(sessionId: string): string {
    return `${this.storagePrefix}${sessionId}`;
  }

  private saveSession(session: ChatSession): void {
    try {
      localStorage.setItem(this.getSessionKey(session.id), JSON.stringify(session));
    } catch (error) {
      console.error('ChatStorage: Error saving session:', error);
    }
  }

  private getSession(sessionId: string): ChatSession | null {
    try {
      const sessionData = localStorage.getItem(this.getSessionKey(sessionId));
      return sessionData ? JSON.parse(sessionData) as ChatSession : null;
    } catch (error) {
      console.warn(`ChatStorage: Error parsing session ${sessionId}:`, error);
      localStorage.removeItem(this.getSessionKey(sessionId)); // Remove corrupted session
      return null;
    }
  }

  private updateSessionExpiry(sessionId: string): void {
    const session = this.getSession(sessionId);
    if (session) {
      session.expiresAt = Date.now() + SESSION_EXPIRY_MS;
      this.saveSession(session);
    }
  }
}

export const chatStorage = new ChatStorage();