<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Test - sparkyelectrical</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-links {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        .test-link {
            display: block;
            padding: 15px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        .test-link:hover {
            background: #5a6fd8;
        }
        .demo-link {
            background: #f59e0b;
        }
        .demo-link:hover {
            background: #d97706;
        }
        .config-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .config-info h3 {
            margin-top: 0;
            color: #333;
        }
        .config-info code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 QuoteAI Portal Test Page</h1>
        <p>Use this page to test the portal functionality for different tradie names.</p>
        
        <div class="test-links">
            <a href="chat/demo/" class="test-link demo-link">
                🧪 Demo Portal (/chat/demo) - Mock Responses
            </a>
            <a href="portal.html?tradie=sparkyelectrical" class="test-link">
                ⚡ Sparky Electrical Portal - Real API
            </a>
            <a href="portal.html?tradie=aquaflow-plumbers" class="test-link">
                🔧 AquaFlow Plumbers Portal - Real API
            </a>
            <a href="portal.html?tradie=handy-repairs" class="test-link">
                🔨 Handy Repairs Portal - Real API
            </a>
        </div>
        
        <div class="config-info">
            <h3>🔧 Current Configuration</h3>
            <p><strong>API URL:</strong> <code>https://0935-86-48-8-229.ngrok-free.app</code></p>
            <p><strong>API Key:</strong> <code>';C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL</code></p>
            <p><strong>Demo Mode:</strong> Only for <code>tradieName === 'demo'</code></p>
            <p><strong>Real API:</strong> All other tradie names connect to Flask backend</p>
        </div>
        
        <div class="config-info">
            <h3>🧪 Testing Instructions</h3>
            <ol>
                <li><strong>Demo Portal:</strong> Should show demo banner and use mock responses</li>
                <li><strong>Real Portals:</strong> Should connect to Flask backend and use real API</li>
                <li><strong>URL Format:</strong> <code>/chat/tradie-name</code> extracts tradie name dynamically</li>
                <li><strong>Customer Name:</strong> Passed as <code>customer_name: "tradie-name"</code> to backend</li>
            </ol>
        </div>
        
        <div class="config-info">
            <h3>🔍 What to Check</h3>
            <ul>
                <li>Demo portal shows orange banner at top</li>
                <li>Real portals have no banner (clean interface)</li>
                <li>Page title updates with tradie name</li>
                <li>Console shows correct API calls for real portals</li>
                <li>Chat responses come from backend (not demo text)</li>
                <li>No horizontal clipping on desktop</li>
            </ul>
        </div>
        
        <div id="api-status"></div>
        
        <script>
            // Test API connectivity
            async function testAPIConnection() {
                const statusDiv = document.getElementById('api-status');
                
                try {
                    const response = await fetch('https://0935-86-48-8-229.ngrok-free.app/v1/ping', {
                        method: 'GET',
                        mode: 'cors'
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        statusDiv.innerHTML = '<div class="status success">✅ API Server is online and responding</div>';
                    } else {
                        statusDiv.innerHTML = '<div class="status error">❌ API Server responded with error: ' + response.status + '</div>';
                    }
                } catch (error) {
                    statusDiv.innerHTML = '<div class="status error">❌ Cannot connect to API Server: ' + error.message + '</div>';
                }
            }
            
            // Test on page load
            testAPIConnection();
            
            // Add click handlers to log which portal is being tested
            document.querySelectorAll('.test-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    const href = e.target.getAttribute('href');
                    console.log('🧪 Testing portal:', href);
                });
            });
        </script>
    </div>
</body>
</html>
