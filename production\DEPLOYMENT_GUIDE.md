# QuoteAI Portal Production Deployment Guide

## 🚀 Quick Deployment to getquoteai.com

This guide will help you deploy the QuoteAI portal to your live website at `getquoteai.com`.

## 📦 What You're Deploying

### Directory Structure
```
production/
├── chat/                    # Chat portal directory
│   ├── demo/               # Demo portal at /chat/demo
│   │   └── index.html      # Demo-specific portal
│   └── .htaccess           # URL routing configuration
├── assets/                 # Portal assets
│   ├── portal.js           # React portal application
│   └── portal.css          # Portal styling
├── portal.html             # Generic portal for any tradie
└── DEPLOYMENT_GUIDE.md     # This guide
```

### Portal URLs After Deployment
- **Demo Portal**: `getquoteai.com/chat/demo`
- **Any Tradie**: `getquoteai.com/chat/tradie-name`
- **Examples**:
  - `getquoteai.com/chat/aquaflow-plumbers`
  - `getquoteai.com/chat/sparky-electrical`

## 🔧 Pre-Deployment Configuration

### 1. Update API URL
**IMPORTANT**: Before deploying, update the API URL in these files:

**File: `assets/portal.js`** (Line 15)
```javascript
apiUrl: 'https://api.getquoteai.com', // Replace with your actual API URL
```

**File: `portal.html`** (Line 238)
```javascript
apiUrl: 'https://api.getquoteai.com', // Replace with your actual API URL
```

**File: `chat/demo/index.html`** (Line 202)
```javascript
apiUrl: 'https://api.getquoteai.com', // Replace with your actual API URL
```

### 2. Verify API Key
The API key is already configured. If you need to change it, update:
```javascript
apiKey: "';C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL"
```

## 📤 Deployment Steps

### Step 1: Upload Files
Upload the entire `production/` directory contents to your website root:

```
getquoteai.com/
├── chat/                   # Upload this directory
│   ├── demo/
│   │   └── index.html
│   └── .htaccess
├── assets/                 # Upload this directory
│   ├── portal.js
│   └── portal.css
└── portal.html             # Upload this file
```

### Step 2: Set File Permissions
Ensure proper file permissions:
- **Directories**: 755
- **HTML files**: 644
- **CSS/JS files**: 644
- **.htaccess**: 644

### Step 3: Test the Deployment
1. **Test Demo Portal**: Visit `getquoteai.com/chat/demo`
2. **Test Generic Portal**: Visit `getquoteai.com/chat/test-tradie`
3. **Check Console**: Open browser dev tools and check for errors

## 🧪 Testing Checklist

### Demo Portal (`/chat/demo`)
- [ ] Page loads without errors
- [ ] Demo banner is visible
- [ ] Chat interface appears
- [ ] Can send messages
- [ ] Image upload works
- [ ] Mobile responsive

### Generic Portal (`/chat/any-name`)
- [ ] Page loads without errors
- [ ] No demo banner (clean interface)
- [ ] Tradie name extracted from URL
- [ ] Chat interface appears
- [ ] All functionality works

### Error Scenarios
- [ ] `/chat/` (no tradie) redirects to demo
- [ ] Invalid URLs show error message
- [ ] JavaScript disabled shows fallback

## 🔧 Server Configuration

### Apache .htaccess
The included `.htaccess` file handles:
- URL routing for `/chat/tradie-name`
- Security headers
- File compression
- Caching rules

### Nginx (Alternative)
If using Nginx, add this to your server config:
```nginx
location /chat/ {
    try_files $uri $uri/ /portal.html;
}

location /chat/demo {
    try_files $uri $uri/ /chat/demo/index.html;
}
```

## 🐛 Troubleshooting

### Common Issues

**Portal doesn't load:**
- Check file paths and permissions
- Verify API URL is correct
- Check browser console for errors

**404 errors:**
- Ensure `.htaccess` is uploaded
- Check server supports URL rewriting
- Verify file permissions

**API connection fails:**
- Update API URL in configuration files
- Check CORS settings on your API server
- Verify API key is correct

**Mobile layout issues:**
- Test on actual devices
- Check viewport meta tag
- Clear browser cache

### Debug Mode
Add this to any portal file for debugging:
```javascript
// Add after window.QUOTE_AI_CONFIG
console.log('Portal Debug:', {
    config: window.QUOTE_AI_CONFIG,
    url: window.location.href,
    tradieName: window.QUOTE_AI_CONFIG.tradieName
});
```

## 🔒 Security Considerations

### Included Security Features
- XSS protection headers
- Content type validation
- Frame options protection
- Referrer policy

### Additional Recommendations
- Use HTTPS only
- Implement rate limiting on API
- Monitor for unusual traffic
- Regular security updates

## 📊 Monitoring

### Key Metrics to Track
- Portal page views
- Chat interactions
- Error rates
- Mobile vs desktop usage
- Loading times

### Analytics Setup
Add Google Analytics or similar:
```javascript
// Add to portal files
gtag('event', 'portal_load', {
    'tradie_name': window.QUOTE_AI_CONFIG.tradieName,
    'page_location': window.location.href
});
```

## 🚀 Next Steps

After successful deployment:
1. **Test thoroughly** on different devices
2. **Monitor performance** and user interactions
3. **Gather feedback** from users
4. **Add more tradie-specific customizations**
5. **Integrate with booking systems**

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Verify all files uploaded correctly
3. Test API endpoints directly
4. Check server error logs

---

**Ready to deploy?** Follow the steps above and your QuoteAI portal will be live at `getquoteai.com/chat/demo`!
