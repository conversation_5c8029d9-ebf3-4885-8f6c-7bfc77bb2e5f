{"name": "quoteai-widget", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:portal": "vite --open /portal-test.html", "build": "tsc -b && vite build", "build:portal": "tsc -b && vite build --mode portal", "lint": "eslint .", "preview": "vite preview", "preview:portal": "vite preview --open /portal-test.html", "deploy": "npm run build && firebase deploy --only hosting", "test:portal": "npm run dev:portal"}, "dependencies": {"@firebase/analytics": "^0.10.12", "esbuild": "^0.25.5", "firebase": "^11.6.0", "react": "^18.3.1", "react-chatbot-kit": "^2.2.2", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "rollup-plugin-visualizer": "^5.14.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "5.4.5"}}