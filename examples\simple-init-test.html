<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple QuoteAI Widget Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #1b8ae4;
    }
    .test-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background: #0c6cb3;
    }
    .log {
      background: #000;
      color: #0f0;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      height: 150px;
      overflow-y: auto;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Simple QuoteAI Widget Test</h1>
    
    <div class="test-panel">
      <h2>Test Controls</h2>
      <button id="initButton">Initialize Widget</button>
      <button id="clearLog">Clear Log</button>
      
      <div class="log" id="logOutput"></div>
    </div>
  </div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the built widget -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">

  <script>
    // Simple logging function
    function log(message) {
      const logOutput = document.getElementById('logOutput');
      const timestamp = new Date().toLocaleTimeString();
      logOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
      logOutput.scrollTop = logOutput.scrollHeight;
      console.log(`[${timestamp}] ${message}`);
    }
    
    // Initialize when the page loads
    document.addEventListener('DOMContentLoaded', () => {
      log('Page loaded');
      
      // Check if initChatWidget is available
      if (typeof window.initChatWidget === 'function') {
        log('✅ initChatWidget is available');
      } else {
        log('❌ initChatWidget is NOT available');
      }
      
      // Set up button event listeners
      document.getElementById('initButton').addEventListener('click', () => {
        try {
          log('Initializing widget...');
          
          if (typeof window.initChatWidget === 'function') {
            const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 9);
            window.initChatWidget({
              clientId: "test-client-simple",
              apiKey: "test-key-simple",
              uuid: clientUUID,
              apiUrl: "http://localhost:8080",
            });
            log(`Widget initialized with UUID: ${clientUUID}`);
          } else {
            log('ERROR: initChatWidget is not a function');
          }
        } catch (error) {
          log(`ERROR: ${error.message}`);
        }
      });
      
      document.getElementById('clearLog').addEventListener('click', () => {
        document.getElementById('logOutput').innerHTML = '';
        log('Log cleared');
      });
    });
  </script>
</body>
</html>
