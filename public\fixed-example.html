<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Widget - Fixed Example</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .code-block {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      font-family: monospace;
      margin: 20px 0;
    }
    .note {
      background-color: #e6f7ff;
      border-left: 4px solid #1890ff;
      padding: 10px 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Widget - Fixed Example</h1>
    <p>This example demonstrates how to properly load the QuoteAI widget with React dependencies.</p>
    
    <div class="note">
      <strong>Important:</strong> The widget requires React and ReactDOM to be loaded before the widget script.
    </div>
    
    <h2>Integration Code</h2>
    <div class="code-block">
      <pre>&lt;!-- 1. Load React and ReactDOM (required dependencies) --&gt;
&lt;script src="https://unpkg.com/react@18.0.0/umd/react.production.min.js"&gt;&lt;/script&gt;
&lt;script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.production.min.js"&gt;&lt;/script&gt;

&lt;!-- 2. Load the widget script and styles --&gt;
&lt;script src="https://widget.quoteai.com/chatbot-widget.umd.js"&gt;&lt;/script&gt;
&lt;link rel="stylesheet" href="https://widget.quoteai.com/style.css"&gt;

&lt;!-- 3. Initialize the widget --&gt;
&lt;script&gt;
  document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.initChatWidget === 'function') {
      window.initChatWidget({
        apiKey: "YOUR_API_KEY",
        websiteUrl: "your-domain.com",
        apiUrl: "https://api.quoteai.com" // Optional
      });
    }
  });
&lt;/script&gt;</pre>
    </div>
  </div>

  <!-- 1. Load React and ReactDOM (required dependencies) -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.production.min.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.production.min.js"></script>
  
  <!-- 2. Load the widget script and styles -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">
  
  <!-- 3. Initialize the widget -->
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      if (typeof window.initChatWidget === 'function') {
        window.initChatWidget({
          apiKey: ";C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL",
          websiteUrl: "example.com",
          apiUrl: "http://localhost:8080"
        });
        console.log('Widget initialized successfully!');
      } else {
        console.error('Widget initialization failed: initChatWidget function not available');
      }
    });
  </script>
</body>
</html>
