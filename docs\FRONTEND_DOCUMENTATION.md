# QuoteAI Front-End Documentation

## Table of Contents

1. [Overview](#overview)
2. [Project Structure](#project-structure)
3. [Design System](#design-system)
4. [Components](#components)
5. [Integration Guide](#integration-guide)
6. [Development Guide](#development-guide)
7. [Responsive Design](#responsive-design)
8. [Assets](#assets)

## Overview

QuoteAI is a chat widget designed to be embedded in client websites to provide instant quote services. The front-end is built using React and TypeScript, with a focus on creating a lightweight, responsive, and customizable chat interface.

### Technologies Used

- **React**: UI library for building the component-based interface
- **TypeScript**: For type-safe code
- **Vite**: Build tool for fast development and optimized production builds
- **CSS**: Custom styling for the chat widget
- **React Chatbot Kit**: Base library for chatbot functionality

### Design Philosophy

The QuoteAI widget follows these design principles:

- **Non-intrusive**: The widget appears as a floating button that expands only when clicked
- **Responsive**: Works well on all device sizes
- **Lightweight**: Minimal impact on the host website's performance
- **Branded**: Customizable to match client branding
- **User-friendly**: Simple, intuitive interface for end users

## Project Structure

```
app/
├── public/                  # Static assets
│   ├── quoteai.png          # Logo image
│   ├── styles.css           # Example client website styles
│   └── example-client.html  # Example implementation
├── src/
│   ├── styles/
│   │   └── widget.css       # Widget-specific styles
│   ├── ChatWidget.tsx       # Main widget component
│   └── main.tsx            # Entry point
├── vite.config.ts           # Vite configuration
└── package.json             # Dependencies and scripts
```

## Design System

### Color Palette

The widget uses a consistent color scheme:

- **Primary Color**: `#1b8ae4` - Used for the chat button, user messages, and header
- **Secondary Color**: `#53a2be` - Used for hover states
- **Background**: `white` - For the chat container
- **Text**: `#333` (dark gray) for normal text, `white` for text on colored backgrounds

### Typography

- **Font Family**: 'Open Sans' for body text, 'Montserrat' for headers
- **Font Sizes**:
  - Button text: `20px`
  - Chat header: `18px`
  - Message text: `14px`
  - Input text: `14px`

### Spacing

- Consistent padding of `16px` for containers
- Gap of `12px` between chat messages
- Border radius of `12px` for the chat container and messages

## Components

### ChatWidget

The main component that renders the entire chat interface.

#### Props

```typescript
interface ChatWidgetProps {
  clientId: string;  // Client identifier
  apiKey: string;    // API key for authentication
  uuid: string;      // Unique user identifier
}
```

#### State

- `threadId`: Stores the conversation thread ID from the backend
- `isLoaded`: Tracks whether the widget has initialized
- `error`: Stores any error messages
- `isChatOpen`: Controls the visibility of the chat window
- `messages`: Array of user and bot messages
- `inputValue`: Current value of the input field
- `loading`: Indicates when the bot is processing a message

#### Key Functions

- `initializeConversation()`: Sets up the initial connection with the backend
- `handleUserMessage()`: Processes and sends user messages to the backend
- `toggleChat()`: Opens and closes the chat window

### UI Elements

1. **Chat Button**
   - Floating button in the bottom-right corner
   - Contains emoji and text "Get a Free Quote!"
   - Expands to full chat window when clicked

2. **Chat Container**
   - Main window that appears when the button is clicked
   - Contains header, messages area, and input area

3. **Chat Header**
   - Contains logo, title, and close button
   - Branded with QuoteAI colors and logo

4. **Messages Area**
   - Scrollable container for conversation history
   - Different styling for user messages (right-aligned, blue) and bot messages (left-aligned, gray)
   - Loading animation when waiting for bot response

5. **Input Area**
   - Text input for user messages
   - Send button
   - Additional action buttons (attachment, emoji)

## Integration Guide

### Basic Integration

To integrate the QuoteAI widget into a client website:

1. Include the required scripts in the HTML:

```html
<!-- Load React and ReactDOM from CDN -->
<script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
<script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

<!-- Load chatbot widget -->
<script type="module" src="https://quoteai-firebase.web.app/chatbot-widget.umd.js"></script>
```

2. Initialize the widget with client credentials:

```html
<script>
  document.addEventListener('DOMContentLoaded', (event) => {
    if (typeof window.initChatWidget === 'function') {
      const clientUUID = "your_client_uuid"; // Replace with actual UUID
      window.initChatWidget({
        clientId: "your-client-id",
        apiKey: "your-api-key",
        uuid: clientUUID,
      });
    } else {
      console.error('initChatWidget is not available');
    }
  });
</script>
```

### Configuration Options

The widget accepts the following configuration parameters:

- `clientId`: Unique identifier for the client website
- `apiKey`: Authentication key for the QuoteAI API
- `uuid`: Unique identifier for the end user (for conversation tracking)

## Development Guide

### Setup

1. Clone the repository
2. Install dependencies:
   ```
   cd app
   npm install
   ```

### Development Server

Run the development server:
```
npm run dev
```

This will start a local server at http://localhost:5173/ and open the example client page.

### Building for Production

Build the widget for production:
```
npm run build
```

This creates optimized files in the `dist` directory:
- `chatbot-widget.es.js` (ESM format)
- `chatbot-widget.umd.js` (UMD format)

### Environment Configuration

The widget uses environment variables to configure the API endpoint:

```
VITE_API_BASE_URL=https://api.quoteai.com
```

These are stored in the following files:

- `.env`: Default values
- `.env.development`: Development environment values (used with `npm run dev`)
- `.env.production`: Production environment values (used with `npm run build`)

To change the API endpoint for a specific environment, modify the corresponding file.

### Deployment

The widget is configured for deployment to Firebase Hosting:

1. Set the correct API URL in `.env.production`
2. Build the project:
   ```
   npm run build
   ```
3. Deploy using Firebase CLI:
   ```
   firebase deploy --only hosting
   ```

## Responsive Design

The widget is designed to be fully responsive:

- On desktop, the chat window appears as a modal in the bottom-right corner
- On mobile devices, the chat window takes up more screen space for better usability
- Font sizes and padding adjust based on screen size
- Input area remains accessible even on small screens

Media queries are used to adjust the layout for screens smaller than 768px:
```css
@media (max-width: 768px) {
  .chat-container {
    width: 90%;
    height: 70vh;
    right: 5%;
  }
}
```

## API Communication

### Headers

The widget uses the following headers for API requests:

```javascript
headers: {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${apiKey}`,
  'Client-ID': clientId,
  'x-api-key': apiKey,
}
```

- `Content-Type`: Specifies the format of the request body (JSON)
- `Authorization`: Bearer token authentication using the provided API key
- `Client-ID`: Identifies the client website making the request
- `x-api-key`: API key for authentication (added for compatibility with API gateways)

### Endpoints

The widget communicates with two main endpoints:

1. **Initialize Conversation**: `POST /initiate_conversation`
   - Creates a new conversation thread
   - Returns a thread ID for subsequent messages

2. **Send Message**: `POST /chat`
   - Sends user messages to the backend
   - Receives bot responses

## Assets

### Logo

- `quoteai.png`: The QuoteAI logo used in the chat header
- Dimensions: 28px × 28px (displayed size)
- Background: white with border radius for circular appearance

### Icons

The widget uses emoji icons for simplicity and cross-platform compatibility:
- 💬 Chat button icon
- 📎 Attachment icon
- 😊 Emoji icon
- × Close button
