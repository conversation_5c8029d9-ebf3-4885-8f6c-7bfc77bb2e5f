{"clients": {"client_id": {"basic_info": {"client_id": "string", "website_url": "string", "company_name": "string", "business_abn": "string", "api_key": "string", "user_type": "string", "status": "string"}, "tradie_details": {"tradie_type": "string", "specializations": ["string"], "service_areas": {"suburbs": ["string"], "postcodes": ["string"], "radius_km": "number"}, "business_hours": {"monday": {"open": "string", "close": "string"}, "tuesday": {"open": "string", "close": "string"}, "wednesday": {"open": "string", "close": "string"}, "thursday": {"open": "string", "close": "string"}, "friday": {"open": "string", "close": "string"}, "saturday": {"open": "string", "close": "string"}, "sunday": {"open": "string", "close": "string"}, "timezone": "string"}}, "contact_info": {"primary_email": "string", "secondary_emails": ["string"], "phone": "string", "mobile": "string", "address": {"street": "string", "suburb": "string", "state": "string", "postcode": "string"}}, "ai_settings": {"greeting_message": "string", "custom_instructions": "string", "response_tone": "string", "max_quote_amount": "number"}, "subscription": {"plan": "string", "start_date": "timestamp", "renewal_date": "timestamp", "payment_status": "string"}, "metrics": {"total_conversations": "number", "total_quotes_generated": "number", "conversion_rate": "number", "last_activity": "timestamp"}, "timestamps": {"created_at": "timestamp", "updated_at": "timestamp", "last_login": "timestamp"}}}}