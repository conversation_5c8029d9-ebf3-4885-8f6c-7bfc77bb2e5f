<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct Widget Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .debug-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .debug-log {
      background: #000;
      color: #0f0;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      height: 200px;
      overflow-y: auto;
      margin-top: 10px;
    }
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background: #0c6cb3;
    }
    #widget-container {
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>Direct Widget Test</h1>
  
  <div class="debug-panel">
    <h2>Debug Controls</h2>
    <button id="createWidget">Create Widget Directly</button>
    <button id="clearLog">Clear Log</button>
    
    <div class="debug-log" id="debugLog"></div>
  </div>
  
  <div id="widget-container"></div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <script>
    // Debug logging function
    function log(message) {
      const debugLog = document.getElementById('debugLog');
      const timestamp = new Date().toLocaleTimeString();
      debugLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
      debugLog.scrollTop = debugLog.scrollHeight;
    }
    
    document.addEventListener('DOMContentLoaded', () => {
      log('Page loaded');
      
      document.getElementById('createWidget').addEventListener('click', () => {
        createWidgetDirectly();
      });
      
      document.getElementById('clearLog').addEventListener('click', () => {
        document.getElementById('debugLog').innerHTML = '';
        log('Log cleared');
      });
    });
    
    async function createWidgetDirectly() {
      try {
        log('Creating widget directly...');
        
        // Create a container for the widget
        const container = document.getElementById('widget-container');
        container.innerHTML = ''; // Clear previous content
        
        // Create a new div for the widget
        const widgetRoot = document.createElement('div');
        container.appendChild(widgetRoot);
        
        // Import the ChatWidget component directly
        log('Importing ChatWidget component...');
        
        // Create a script element to load the module
        const script = document.createElement('script');
        script.type = 'module';
        script.textContent = `
          import React from 'react';
          import { createRoot } from 'react-dom/client';
          import ChatWidget from '/src/ChatWidget.tsx';
          
          // Log that we're inside the module
          console.log('Inside module script');
          
          // Create the widget
          const root = document.getElementById('widget-container').querySelector('div');
          const reactRoot = ReactDOM.createRoot(root);
          
          // Render the widget
          reactRoot.render(
            React.createElement(React.StrictMode, null,
              React.createElement(ChatWidget, {
                clientId: "test-client-direct",
                apiKey: "test-key-direct",
                uuid: "direct-${Math.random().toString(36).substring(2, 9)}",
                apiUrl: "http://localhost:8080"
              })
            )
          );
          
          console.log('Widget rendered');
        `;
        
        document.body.appendChild(script);
        log('Module script added to page');
      } catch (e) {
        log(`Error creating widget: ${e.message}`);
        console.error(e);
      }
    }
  </script>
</body>
</html>
