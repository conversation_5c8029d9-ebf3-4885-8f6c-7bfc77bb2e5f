import { useState, useEffect, useCallback } from 'react';
import { playNotificationSound } from '../utils/notificationSound';
import type { Message } from '../types';

export interface UsePortalModeReturn {
  isConnected: boolean;
  connectionError: string | null;
  handleNewBotMessage: (lastMessage?: Message) => void;
  retryConnection: () => void;
  setConnectionStatus: (connected: boolean, error?: string) => void;
}

export const usePortalMode = (): UsePortalModeReturn => {
  const [isConnected, setIsConnected] = useState(true);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const setConnectionStatus = useCallback((connected: boolean, error?: string) => {
    setIsConnected(connected);
    setConnectionError(error || null);
    
    if (!connected && error) {
      console.error('QuoteAI Portal: Connection error:', error);
    }
  }, []);

  const retryConnection = useCallback(() => {
    setRetryCount(prev => prev + 1);
    setConnectionError(null);
    setIsConnected(true);
    console.info('QuoteAI Portal: Retrying connection...');
  }, []);

  const handleNewBotMessage = useCallback((lastMessage?: Message) => {
    if (lastMessage && lastMessage.type === 'bot') {
      // In portal mode, always play notification sound for new messages
      playNotificationSound();
    }
  }, []);

  // Auto-retry connection after failures
  useEffect(() => {
    if (!isConnected && connectionError && retryCount < 3) {
      const timeout = setTimeout(() => {
        retryConnection();
      }, 5000 * (retryCount + 1)); // Exponential backoff

      return () => clearTimeout(timeout);
    }
  }, [isConnected, connectionError, retryCount, retryConnection]);

  // Check connection status periodically
  useEffect(() => {
    const checkConnection = async () => {
      try {
        // Simple connectivity check
        await fetch(window.QUOTE_AI_CONFIG?.apiUrl || 'http://localhost:8080', {
          method: 'HEAD',
          mode: 'no-cors',
        });
        setConnectionStatus(true);
      } catch (error) {
        setConnectionStatus(false, 'Failed to establish connection to server');
      }
    };

    // Check immediately and then every 30 seconds
    checkConnection();
    const interval = setInterval(checkConnection, 30000);

    return () => clearInterval(interval);
  }, [setConnectionStatus]);

  return {
    isConnected,
    connectionError,
    handleNewBotMessage,
    retryConnection,
    setConnectionStatus,
  };
};
