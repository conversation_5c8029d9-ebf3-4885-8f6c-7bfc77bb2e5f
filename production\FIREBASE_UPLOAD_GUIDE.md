# 🔥 Firebase CDN Upload Guide

## 📦 **Files to Upload to Firebase**

Upload these exact files from your `dist/` directory to Firebase Hosting:

### **Required Files:**
```
dist/portal-chat.es.js                    → /portal-chat.es.js
dist/styles/portal-BYt42wO3.css          → /styles/portal-BYt42wO3.css  
dist/styles/ChatWidget-Cz8wUhVr.css      → /styles/ChatWidget-Cz8wUhVr.css
dist/styles/ChatWidget-CA2f4Xlc.js       → /styles/ChatWidget-CA2f4Xlc.js
```

### **Optional (for debugging):**
```
dist/portal-chat.es.js.map               → /portal-chat.es.js.map
dist/styles/ChatWidget-CA2f4Xlc.js.map   → /styles/ChatWidget-CA2f4Xlc.js.map
```

## 🚀 **Firebase Upload Commands**

### **Option 1: Firebase CLI**
```bash
# Initialize Firebase (if not done already)
firebase init hosting

# Copy files to public directory
cp dist/portal-chat.es.js public/
cp -r dist/styles public/

# Deploy to Firebase
firebase deploy --only hosting
```

### **Option 2: Firebase Console**
1. Go to Firebase Console → Hosting
2. Upload files manually:
   - `portal-chat.es.js` → root directory
   - `styles/` folder → create styles directory and upload CSS/JS files

## 🔧 **Update Portal Production Page**

After uploading to Firebase, update `portal-production.html` with your actual Firebase URL:

**Replace:**
```html
https://your-firebase-project.web.app/
```

**With your actual Firebase URL:**
```html
https://your-actual-project-id.web.app/
```

**Example:**
```html
<!-- Portal Styles from Firebase CDN -->
<link rel="stylesheet" href="https://quoteai-portal.web.app/styles/portal-BYt42wO3.css">
<link rel="stylesheet" href="https://quoteai-portal.web.app/styles/ChatWidget-Cz8wUhVr.css">

<!-- Portal Script from Firebase CDN -->
<script type="module" src="https://quoteai-portal.web.app/portal-chat.es.js"></script>
```

## 🌐 **Server Configuration**

### **Apache (.htaccess)**
Create or update `.htaccess` in your website root:
```apache
RewriteEngine On

# Route /chat/tradie-name to portal
RewriteRule ^chat/([^/]+)/?$ portal-production.html [L]

# Handle demo specifically (optional - same result)
RewriteRule ^chat/demo/?$ portal-production.html [L]

# Fallback: redirect /chat/ to demo
RewriteRule ^chat/?$ chat/demo/ [R=302,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
```

### **Nginx**
Add to your server configuration:
```nginx
location ~ ^/chat/([^/]+)/?$ {
    try_files $uri /portal-production.html;
}

location = /chat/ {
    return 302 /chat/demo/;
}

# Security headers
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options SAMEORIGIN;
add_header X-XSS-Protection "1; mode=block";
```

## 🧪 **Testing Your Deployment**

### **1. Test Firebase CDN**
Visit your Firebase URLs directly:
- `https://your-project.web.app/portal-chat.es.js` (should download JS file)
- `https://your-project.web.app/styles/portal-BYt42wO3.css` (should show CSS)

### **2. Test Portal URLs**
- `getquoteai.com/chat/demo` → Demo mode with banner
- `getquoteai.com/chat/sparkyelectrical` → Production mode, real API
- `getquoteai.com/chat/test-tradie` → Production mode, real API

### **3. Check Browser Console**
Look for:
- ✅ No 404 errors for CSS/JS files
- ✅ No MIME type errors
- ✅ Portal initialization messages
- ✅ API calls to your Flask backend (for non-demo tradies)

## 🔍 **Troubleshooting**

### **404 Errors**
- Check Firebase file paths match exactly
- Verify Firebase hosting is deployed
- Check server URL routing configuration

### **MIME Type Errors**
- Ensure Firebase is serving correct content types
- Check `.js` files have `application/javascript` MIME type
- Check `.css` files have `text/css` MIME type

### **Portal Not Loading**
- Check browser console for errors
- Verify React dependencies load correctly
- Check if `portal-root` element exists

### **API Not Working**
- Verify Flask backend is running
- Check ngrok URL is accessible
- Look for CORS errors in console

## ✅ **Success Checklist**

- [ ] Files uploaded to Firebase CDN
- [ ] Firebase URLs updated in portal-production.html
- [ ] Server URL routing configured
- [ ] Demo portal works (`/chat/demo`)
- [ ] Production portal works (`/chat/sparkyelectrical`)
- [ ] Real API calls working for production tradies
- [ ] No console errors
- [ ] Mobile responsive

## 🎯 **Final Result**

After successful deployment:
- **Demo Portal**: `getquoteai.com/chat/demo` → MockChatWidget with demo banner
- **Production Portals**: `getquoteai.com/chat/any-tradie` → Real ChatWidget → Flask backend
- **Fast Loading**: All assets served from Firebase CDN
- **No File Issues**: Proper MIME types and paths

Your portal will be production-ready and using your existing, tested code! 🚀
