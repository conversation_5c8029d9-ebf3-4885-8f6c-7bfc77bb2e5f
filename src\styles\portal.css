/* Portal Mode Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

#portal-root {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Portal Container */
.portal-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Portal Header - DISABLED for cleaner interface */
/*
.portal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.8s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.portal-branding {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.portal-logo {
  font-size: 3rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.portal-title h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.portal-title p {
  margin: 0.25rem 0 0 0;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

/* Connection Status */
.portal-status {
  display: flex;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.connection-status.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.connection-status.offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.connection-status.online .status-indicator {
  background: #22c55e;
}

.connection-status.offline .status-indicator {
  background: #ef4444;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}
*/

/* Portal Chat Area - Three-panel layout container */
.portal-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background: white;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.8s ease-out 0.2s both;
  /* Ensure full height for three-panel layout */
  height: 100vh;
  position: relative;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Error and Warning Banners */
.portal-error-banner,
.portal-warning-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  margin: 0.5rem 1rem;
  border-radius: 12px;
  animation: slideInFromTop 0.5s ease-out;
}

.portal-error-banner {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #f87171;
  color: #dc2626;
}

.portal-warning-banner {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  color: #d97706;
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.error-icon,
.warning-icon {
  font-size: 1.25rem;
}

.error-message,
.warning-message {
  flex: 1;
  font-weight: 500;
}

.retry-btn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Portal Error Page */
.portal-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-content {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.error-content .error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-content h1 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.error-content p {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

/* Mobile-First Responsive Design */
@media (max-width: 768px) {
  /* .portal-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  } */

  .portal-title h1 {
    font-size: 1.5rem;
  }

  .portal-chat-area {
    border-radius: 15px 15px 0 0;
  }

  .error-content {
    margin: 1rem;
    padding: 2rem;
  }

  /* Mobile chat input optimizations - Consistent with desktop */
  .chat-input.portal-mode {
    /* Inherit desktop styles - no override needed */
    padding: 1rem;
    /* Add safe area for devices with notches */
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    /* Enhanced mobile styling */
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  }

  .chat-input.portal-mode .input-row {
    gap: 0.5rem;
    max-width: 100%;
  }

  .chat-input.portal-mode .portal-textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
    max-height: 100px; /* Smaller max height on mobile */
    /* Improve touch interaction */
    -webkit-appearance: none;
    appearance: none;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Mobile placeholder optimization - smaller font for better visibility */
  .chat-input.portal-mode .portal-textarea::placeholder {
    font-size: 14px;
    color: #9ca3af;
    opacity: 1;
    /* Add subtle animation for long placeholder text */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .chat-input.portal-mode .portal-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }

  .chat-input.portal-mode .icon-button {
    padding: 0.625rem;
    font-size: 1.125rem;
    min-width: 40px;
    min-height: 40px;
    flex-shrink: 0; /* Prevent buttons from shrinking */
  }

  /* Mobile messages area - consistent with desktop three-panel layout */
  .chat-messages.portal-mode {
    padding: 1rem;
    /* Maintain consistent height calculation */
    height: calc(100vh - 120px); /* Adjust for mobile header height */
    /* Add top padding for fixed header on mobile */
    padding-top: calc(60px + 1rem); /* Account for smaller mobile header */
    /* Add bottom margin to prevent overlap with sticky input */
    margin-bottom: 120px; /* Space for mobile sticky input area */
    /* Improve scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

@media (max-width: 480px) {
  /* .portal-header {
    padding: 0.75rem;
  } */

  .portal-title h1 {
    font-size: 1.25rem;
  }

  .portal-title p {
    font-size: 0.875rem;
  }

  .portal-logo {
    font-size: 2rem;
  }

  /* Extra small mobile optimizations - Consistent with desktop */
  .chat-input.portal-mode {
    /* Inherit desktop fixed positioning */
    padding: 0.75rem;
    padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
    /* Enhanced styling for very small screens */
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.2);
  }

  .chat-input.portal-mode .input-row {
    gap: 0.375rem;
    max-width: 100%;
  }

  .chat-input.portal-mode .portal-textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.625rem 0.75rem;
    border-radius: 10px;
    max-height: 80px; /* Even smaller max height on very small screens */
  }

  /* Extra small mobile placeholder optimization */
  .chat-input.portal-mode .portal-textarea::placeholder {
    font-size: 13px; /* Even smaller on very small screens */
    color: #9ca3af;
    opacity: 1;
  }

  .chat-input.portal-mode .portal-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.625rem 0.75rem;
    border-radius: 10px;
  }

  .chat-input.portal-mode .icon-button {
    padding: 0.5rem;
    font-size: 1rem;
    min-width: 36px;
    min-height: 36px;
    border-radius: 8px;
    flex-shrink: 0; /* Prevent buttons from shrinking */
  }

  .chat-input.portal-mode .portal-buttons {
    gap: 0.25rem;
    flex-shrink: 0; /* Prevent button container from shrinking */
  }

  /* Optimize message area for small screens - consistent three-panel layout */
  .chat-messages.portal-mode {
    padding: 0.75rem;
    /* Maintain consistent height calculation for small screens */
    height: calc(100vh - 100px); /* Adjust for smaller mobile header */
    /* Add top padding for fixed header on very small screens */
    padding-top: calc(50px + 0.75rem); /* Account for compact header */
    /* Add bottom margin to prevent overlap with sticky input */
    margin-bottom: 100px; /* Space for compact sticky input area */
  }

  /* Make header more compact on very small screens */
  .chat-header.portal-mode {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    /* Ensure fixed positioning works on small screens */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
  }
}

/* Portal Mode Overrides for Chat Components */

/* Hide the floating chat button in portal mode */
.portal-container .chat-button {
  display: none !important;
}

/* Make chat container always visible and full-screen with three-panel layout */
.portal-container .chat-container {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  bottom: auto !important;
  right: auto !important;
  transform: none !important;
  display: flex !important;
  flex-direction: column;
  flex: 1;
  /* Ensure proper layout for three-panel sticky layout */
  overflow: hidden;
  background: #f8fafc; /* Consistent background */
}

/* Ensure chat messages area takes full height in portal mode */
.portal-container .chat-container.portal-mode {
  height: 100%;
}

/* Removed - consolidated into .chat-messages.portal-mode below for consistency */

.chat-header.portal-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001; /* Higher than input to ensure proper stacking */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0;
  font-size: 1.1rem;
  padding: 1rem 1.5rem;
  /* Add shadow for better visual separation */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  /* Ensure it stays above other content */
  backdrop-filter: blur(20px);
  /* Ensure full width coverage */
  width: 100%;
  box-sizing: border-box;
}

.chat-messages.portal-mode {
  flex: 1;
  min-height: 0; /* Allow flex shrinking */
  height: calc(100vh - 140px); /* Full height minus header and input area */
  padding: 1.5rem;
  background: #f8fafc;
  /* Enhanced scrolling experience */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  overflow-y: auto;
  /* Add top padding for fixed header */
  padding-top: calc(70px + 1.5rem); /* Account for fixed header height */
  /* Add bottom margin to prevent overlap with sticky input */
  margin-bottom: 140px; /* Space for sticky input area */
  /* Ensure consistent background fills entire area */
  box-sizing: border-box;
}

/* Custom scrollbar for messages area only */
.chat-messages.portal-mode::-webkit-scrollbar {
  width: 8px;
}

.chat-messages.portal-mode::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin: 8px 0; /* Constrain scrollbar to message area */
}

.chat-messages.portal-mode::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.chat-messages.portal-mode::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Firefox scrollbar styling */
.chat-messages.portal-mode {
  scrollbar-width: thin;
  scrollbar-color: #667eea rgba(0, 0, 0, 0.05);
}

/* Portal Mode Chat Input - Sticky to Bottom for ALL devices */
.chat-input.portal-mode {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid #e2e8f0;
  border-radius: 0;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden; /* Prevent horizontal overflow */
  /* Add shadow for better visual separation */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  /* Ensure it stays above other content */
  backdrop-filter: blur(20px);
}

/* Horizontal input row for portal mode */
.chat-input.portal-mode .input-row {
  display: flex;
  align-items: flex-end; /* Align to bottom for multi-line textarea */
  gap: 0.75rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Portal textarea takes most of the space with auto-expanding */
.chat-input.portal-mode .portal-textarea {
  flex: 1;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8fafc;
  min-height: 44px; /* Minimum touch target for mobile */
  max-height: 120px; /* Limit maximum height */
  line-height: 1.4;
  font-family: inherit;
  resize: none;
  overflow: hidden;
  box-sizing: border-box;
  width: 0; /* Allow flex to control width */
  /* Improve placeholder visibility on thin displays */
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* When textarea has content, allow normal wrapping */
.chat-input.portal-mode .portal-textarea:not(:placeholder-shown) {
  white-space: normal;
}

.chat-input.portal-mode .portal-textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: white;
  outline: none;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Add subtle glow effect when typing */
.chat-input.portal-mode .portal-textarea:not(:placeholder-shown):not(:focus) {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.1);
}

/* TOP-TIER CHATBOT EXPERIENCE ENHANCEMENTS */

/* 1. Message Read Indicators */
.message-container.user .message::after {
  content: '✓';
  position: absolute;
  bottom: 4px;
  right: 8px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  opacity: 0;
  animation: fadeInDelay 1s ease 0.5s forwards;
}

@keyframes fadeInDelay {
  to { opacity: 1; }
}

/* 2. Typing Indicator Enhancement */
.loading-dots {
  background: rgba(255, 255, 255, 0.9);
  padding: 12px 16px;
  border-radius: 18px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 3. Message Hover Effects for Better Interaction */
.message-container:hover .message {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 4. Smooth Message Entrance Animation */
.message-container {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Legacy support for portal-input class (fallback) */
.chat-input.portal-mode .portal-input {
  flex: 1;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8fafc;
  min-height: 44px;
  box-sizing: border-box;
  width: 0; /* Allow flex to control width */
}

.chat-input.portal-mode .portal-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

/* Portal buttons container */
.chat-input.portal-mode .portal-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  flex-wrap: nowrap; /* Prevent wrapping */
  min-width: fit-content;
}

/* Portal mode icon buttons */
.chat-input.portal-mode .icon-button {
  padding: 0.75rem;
  font-size: 1.25rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  min-width: 44px; /* Minimum touch target for mobile */
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
}

.chat-input.portal-mode .icon-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #e2e8f0;
}

/* Active state for better mobile feedback */
.chat-input.portal-mode .icon-button:active {
  transform: scale(0.95);
  background-color: #cbd5e1;
}

/* 5. Premium Input Experience */
.chat-input.portal-mode .input-row {
  position: relative;
}

/* Floating label effect when typing */
.chat-input.portal-mode .portal-textarea:not(:placeholder-shown) + .floating-label,
.chat-input.portal-mode .portal-textarea:focus + .floating-label {
  opacity: 1;
  transform: translateY(-25px) scale(0.8);
}

/* 6. Send Button Pulse Animation When Ready */
.chat-input.portal-mode .send-button:not(:disabled) {
  animation: subtlePulse 2s infinite;
}

@keyframes subtlePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

/* 7. Connection Status Indicator */
.connection-indicator {
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(34, 197, 94, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  z-index: 1001;
  backdrop-filter: blur(10px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.connection-indicator.show {
  opacity: 1;
}

.connection-indicator.offline {
  background: rgba(239, 68, 68, 0.9);
}

/* 8. Smart Scroll to Bottom Button */
.scroll-to-bottom {
  position: fixed;
  bottom: 140px;
  right: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  z-index: 999;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.scroll-to-bottom.show {
  opacity: 1;
  transform: translateY(0);
}

.scroll-to-bottom:hover {
  background: rgba(102, 126, 234, 1);
  transform: scale(1.1);
}

.chat-input.portal-mode .send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.chat-input.portal-mode .send-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.chat-input.portal-mode .send-button:disabled {
  background: #cbd5e1;
  color: #64748b;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Attach and camera button styling */
.chat-input.portal-mode .attach-button:hover {
  background: #dbeafe;
  border-color: #3b82f6;
}

.chat-input.portal-mode .camera-button:hover {
  background: #dcfce7;
  border-color: #22c55e;
}

/* Mobile-specific touch optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .chat-input.portal-mode .icon-button:hover {
    transform: none;
    box-shadow: none;
  }

  /* Add active states for better touch feedback */
  .chat-input.portal-mode .icon-button:active {
    transform: scale(0.95);
    background: #e2e8f0;
  }

  .chat-input.portal-mode .send-button:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  }

  .chat-input.portal-mode .attach-button:active {
    background: #dbeafe;
    border-color: #3b82f6;
  }

  .chat-input.portal-mode .camera-button:active {
    background: #dcfce7;
    border-color: #22c55e;
  }
}

/* Desktop optimizations for three-panel layout */
@media (min-width: 769px) {
  .portal-chat-area {
    border-radius: 0; /* Full-screen on desktop */
    box-shadow: none;
    max-width: none;
  }

  .chat-header.portal-mode {
    padding: 1.25rem 2rem;
    font-size: 1.2rem;
    /* Ensure fixed positioning works on desktop */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
  }

  .chat-messages.portal-mode {
    padding: 2rem;
    height: calc(100vh - 160px); /* Account for larger desktop header */
    /* Add top padding for fixed header on desktop */
    padding-top: calc(80px + 2rem); /* Account for larger desktop header */
    /* Add bottom margin to prevent overlap with sticky input */
    margin-bottom: 160px; /* Space for larger desktop sticky input area */
  }

  .chat-input.portal-mode {
    padding: 1.5rem 2rem;
  }

  /* Enhanced desktop scrollbar */
  .chat-messages.portal-mode::-webkit-scrollbar {
    width: 12px;
  }

  .chat-messages.portal-mode::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 6px;
  }

  .chat-messages.portal-mode::-webkit-scrollbar-thumb {
    border-radius: 6px;
  }
}

/* Landscape orientation optimizations for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .portal-title h1 {
    font-size: 1.25rem;
  }

  .portal-title p {
    font-size: 0.8rem;
  }

  .chat-input.portal-mode {
    padding: 0.75rem 1rem;
  }

  .chat-messages.portal-mode {
    padding: 0.75rem 1rem;
    height: calc(100vh - 90px); /* Adjust for landscape */
    /* Add top padding for fixed header in landscape */
    padding-top: calc(45px + 0.75rem); /* Account for compact landscape header */
    /* Add bottom margin to prevent overlap with sticky input */
    margin-bottom: 90px; /* Space for landscape sticky input area */
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chat-input.portal-mode .icon-button {
    border-width: 0.5px;
  }
}

/* Focus management for accessibility */
.chat-input.portal-mode .portal-input:focus,
.chat-input.portal-mode .portal-textarea:focus {
  outline: none; /* We have custom focus styles */
}

.chat-input.portal-mode .icon-button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth textarea height transitions */
.chat-input.portal-mode .portal-textarea {
  transition: height 0.2s ease-out, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Placeholder styling for textarea */
.chat-input.portal-mode .portal-textarea::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .chat-input.portal-mode .icon-button {
    transition: none;
  }

  .chat-input.portal-mode .portal-input,
  .chat-input.portal-mode .portal-textarea {
    transition: none;
  }
}
