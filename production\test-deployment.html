<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuoteAI Portal Deployment Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-links {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }
        .test-link {
            display: block;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .test-link:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        .demo-link {
            background: rgba(245, 158, 11, 0.8);
        }
        .demo-link:hover {
            background: rgba(245, 158, 11, 1);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.5);
        }
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.5);
        }
        .status.warning {
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid rgba(245, 158, 11, 0.5);
        }
        .config-info {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        .config-info h3 {
            margin-top: 0;
            color: #fbbf24;
        }
        .config-info code {
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            color: #fbbf24;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            position: relative;
            padding-left: 30px;
        }
        .checklist li:before {
            content: "☐";
            position: absolute;
            left: 0;
            font-size: 1.2rem;
        }
        .checklist li.checked:before {
            content: "✅";
        }
        .button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 QuoteAI Portal Deployment Test</h1>
        
        <div class="test-section">
            <h2>🔗 Portal Test Links</h2>
            <div class="test-links">
                <a href="portal-production.html?tradie=demo" class="test-link demo-link">
                    🧪 Demo Portal - MockChatWidget (No API calls)
                </a>
                <a href="portal-production.html?tradie=sparkyelectrical" class="test-link">
                    ⚡ Sparky Electrical - Real ChatWidget → Flask Backend
                </a>
                <a href="portal-production.html?tradie=aquaflow-plumbers" class="test-link">
                    🔧 AquaFlow Plumbers - Real ChatWidget → Flask Backend
                </a>
                <a href="portal-production.html?tradie=handy-repairs" class="test-link">
                    🔨 Handy Repairs - Real ChatWidget → Flask Backend
                </a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Current Configuration</h2>
            <div class="config-info">
                <h3>API Settings</h3>
                <p><strong>Flask Backend:</strong> <code>https://0935-86-48-8-229.ngrok-free.app</code></p>
                <p><strong>API Key:</strong> <code>';C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL</code></p>
                <p><strong>Portal Bundle:</strong> <code>dist/portal-chat.es.js</code></p>
                
                <h3>Widget Logic</h3>
                <p><strong>Demo Mode:</strong> <code>tradieName === 'demo'</code> → MockChatWidget</p>
                <p><strong>Production Mode:</strong> <code>tradieName !== 'demo'</code> → Real ChatWidget</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>✅ Deployment Checklist</h2>
            <ul class="checklist">
                <li id="check-firebase">Upload files to Firebase CDN</li>
                <li id="check-urls">Update Firebase URLs in portal-production.html</li>
                <li id="check-routing">Configure server URL routing</li>
                <li id="check-demo">Test demo portal (shows banner, mock responses)</li>
                <li id="check-production">Test production portal (no banner, real API)</li>
                <li id="check-api">Verify API calls to Flask backend</li>
                <li id="check-mobile">Test mobile responsiveness</li>
                <li id="check-errors">Check for console errors</li>
            </ul>
            
            <div style="margin-top: 20px;">
                <button class="button" onclick="runTests()">🧪 Run Automated Tests</button>
                <button class="button" onclick="checkFirebase()">🔥 Check Firebase CDN</button>
                <button class="button" onclick="checkAPI()">🔌 Check API Connection</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 Test Results</h2>
            <div id="test-results">
                <div class="status warning">
                    ⏳ Click "Run Automated Tests" to check deployment status
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🚀 Next Steps</h2>
            <ol>
                <li><strong>Upload to Firebase:</strong> Follow the Firebase Upload Guide</li>
                <li><strong>Update URLs:</strong> Replace Firebase placeholder URLs</li>
                <li><strong>Configure Server:</strong> Set up URL routing for /chat/tradie-name</li>
                <li><strong>Test Thoroughly:</strong> Verify both demo and production modes</li>
                <li><strong>Go Live:</strong> Deploy to getquoteai.com</li>
            </ol>
        </div>
    </div>
    
    <script>
        function checkItem(id) {
            const item = document.getElementById(id);
            if (item) {
                item.classList.add('checked');
            }
        }
        
        function showResult(message, type = 'success') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
        
        async function checkFirebase() {
            clearResults();
            showResult('🔍 Checking Firebase CDN availability...', 'warning');
            
            // This would check your actual Firebase URLs
            const firebaseUrls = [
                'https://your-firebase-project.web.app/portal-chat.es.js',
                'https://your-firebase-project.web.app/styles/portal-BYt42wO3.css',
                'https://your-firebase-project.web.app/styles/ChatWidget-Cz8wUhVr.css'
            ];
            
            showResult('⚠️ Update Firebase URLs in portal-production.html first', 'warning');
            showResult('📝 Then test the actual Firebase CDN URLs', 'warning');
        }
        
        async function checkAPI() {
            clearResults();
            showResult('🔍 Checking Flask API connection...', 'warning');
            
            try {
                const response = await fetch('https://0935-86-48-8-229.ngrok-free.app/v1/ping', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    showResult('✅ Flask API is online and responding', 'success');
                    checkItem('check-api');
                } else {
                    showResult(`❌ API responded with error: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Cannot connect to Flask API: ${error.message}`, 'error');
                showResult('💡 Make sure your Flask server is running and ngrok is active', 'warning');
            }
        }
        
        function runTests() {
            clearResults();
            showResult('🧪 Running deployment tests...', 'warning');
            
            // Check if we're on the right page
            if (window.location.pathname.includes('test-deployment.html')) {
                showResult('✅ Test page loaded correctly', 'success');
            }
            
            // Check for required elements
            const portalLinks = document.querySelectorAll('.test-link');
            if (portalLinks.length > 0) {
                showResult(`✅ Found ${portalLinks.length} portal test links`, 'success');
            }
            
            // Check browser capabilities
            if (typeof fetch !== 'undefined') {
                showResult('✅ Browser supports modern JavaScript (fetch API)', 'success');
            }
            
            if (typeof Promise !== 'undefined') {
                showResult('✅ Browser supports Promises', 'success');
            }
            
            // Check for React
            if (typeof React !== 'undefined') {
                showResult('✅ React is available', 'success');
            } else {
                showResult('⚠️ React not detected (normal for this test page)', 'warning');
            }
            
            showResult('🎯 Ready for portal testing! Click the portal links above.', 'success');
            
            // Auto-check API
            setTimeout(checkAPI, 1000);
        }
        
        // Add click tracking for portal links
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', (e) => {
                const href = e.target.getAttribute('href');
                const tradie = href.includes('demo') ? 'demo' : href.split('tradie=')[1];
                console.log(`🧪 Testing portal for tradie: ${tradie}`);
                showResult(`🔗 Opening portal for: ${tradie}`, 'success');
            });
        });
    </script>
</body>
</html>
