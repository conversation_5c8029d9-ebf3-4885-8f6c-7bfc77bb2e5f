# QuoteAI Portal Deployment Checklist

## 📋 Pre-Deployment Checklist

### ✅ Configuration Updates
- [ ] **Update API URL** in `assets/portal.js` (line 15)
- [ ] **Update API URL** in `portal.html` (line 238)
- [ ] **Update API URL** in `chat/demo/index.html` (line 202)
- [ ] **Verify API Key** is correct in all files
- [ ] **Test API endpoints** are accessible

### ✅ File Preparation
- [ ] **All files present** in production directory
- [ ] **No WordPress dependencies** remaining
- [ ] **File permissions** set correctly (755 for directories, 644 for files)
- [ ] **.htaccess file** included in chat directory

## 📤 Deployment Steps

### ✅ Upload Files
- [ ] **Upload `chat/` directory** to website root
- [ ] **Upload `assets/` directory** to website root
- [ ] **Upload `portal.html`** to website root
- [ ] **Verify file structure** matches expected layout

### ✅ Server Configuration
- [ ] **URL rewriting enabled** on server
- [ ] **HTTPS configured** and working
- [ ] **CORS headers** configured for API
- [ ] **Security headers** active

## 🧪 Testing Checklist

### ✅ Demo Portal Testing (`/chat/demo`)
- [ ] **Page loads** without errors
- [ ] **Demo banner visible** at top
- [ ] **Chat interface appears** correctly
- [ ] **Can send text messages**
- [ ] **Image upload works**
- [ ] **Mobile responsive** design
- [ ] **Loading states** work properly
- [ ] **Error handling** functions

### ✅ Generic Portal Testing (`/chat/test-tradie`)
- [ ] **Page loads** without errors
- [ ] **No demo banner** (clean interface)
- [ ] **Tradie name extracted** from URL
- [ ] **Page title updates** with tradie name
- [ ] **Chat interface appears** correctly
- [ ] **All functionality works**

### ✅ URL Routing Testing
- [ ] **`/chat/demo`** loads demo portal
- [ ] **`/chat/any-name`** loads generic portal
- [ ] **`/chat/`** (no tradie) redirects to demo
- [ ] **Invalid URLs** show error message
- [ ] **Direct file access** blocked (security)

### ✅ Error Scenario Testing
- [ ] **JavaScript disabled** shows fallback
- [ ] **API connection fails** shows error
- [ ] **Network timeout** handled gracefully
- [ ] **Invalid tradie names** handled properly

### ✅ Mobile Testing
- [ ] **iPhone Safari** works correctly
- [ ] **Android Chrome** works correctly
- [ ] **Tablet landscape** works correctly
- [ ] **Touch interactions** responsive
- [ ] **No horizontal scrolling**
- [ ] **Input focus** doesn't zoom page

### ✅ Performance Testing
- [ ] **Page loads** under 3 seconds
- [ ] **Images optimized** and load quickly
- [ ] **No console errors** in browser
- [ ] **Memory usage** reasonable
- [ ] **Smooth animations** on mobile

## 🔧 Post-Deployment Verification

### ✅ Functionality Verification
- [ ] **Demo user** connects successfully
- [ ] **Chat responses** received from API
- [ ] **Image uploads** processed correctly
- [ ] **Session persistence** works
- [ ] **Error recovery** functions

### ✅ Analytics Setup
- [ ] **Google Analytics** configured (optional)
- [ ] **Portal usage tracking** active
- [ ] **Error monitoring** in place
- [ ] **Performance monitoring** active

### ✅ Security Verification
- [ ] **HTTPS only** access
- [ ] **API key** not exposed in client
- [ ] **File permissions** secure
- [ ] **Directory listing** disabled
- [ ] **Sensitive files** protected

## 🚨 Troubleshooting Guide

### Common Issues and Solutions

**Portal doesn't load:**
- Check file paths and permissions
- Verify API URL is accessible
- Check browser console for errors
- Ensure .htaccess is working

**404 errors on portal URLs:**
- Verify .htaccess uploaded correctly
- Check server supports URL rewriting
- Test with simple HTML file first

**API connection fails:**
- Update API URL in all configuration files
- Check CORS settings on API server
- Verify API key is correct
- Test API endpoints directly

**Mobile layout issues:**
- Test on actual devices, not just browser dev tools
- Check viewport meta tag is present
- Clear browser cache
- Verify touch event handling

**Demo banner not showing:**
- Check if URL is exactly `/chat/demo`
- Verify CSS classes are applied
- Check for JavaScript errors

## 📞 Support Resources

### Debug Information
Add this code to any portal file for debugging:
```javascript
console.log('Portal Debug Info:', {
    config: window.QUOTE_AI_CONFIG,
    url: window.location.href,
    tradieName: window.QUOTE_AI_CONFIG.tradieName,
    userAgent: navigator.userAgent
});
```

### Log Files to Check
- Browser console errors
- Server error logs
- API server logs
- Network requests in dev tools

### Contact Information
- **Technical Issues**: Check browser console and server logs
- **API Issues**: Verify API configuration and test endpoints
- **Server Issues**: Contact hosting provider support

---

## ✅ Final Deployment Confirmation

Once all items are checked:
- [ ] **All tests pass**
- [ ] **No critical errors**
- [ ] **Performance acceptable**
- [ ] **Security verified**
- [ ] **Documentation updated**

**🎉 Portal is ready for production use at `getquoteai.com/chat/demo`!**
